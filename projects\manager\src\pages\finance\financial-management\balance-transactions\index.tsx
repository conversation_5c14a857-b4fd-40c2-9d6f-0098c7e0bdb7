import { getDateTimeRange, SelectTimeRange } from '@/components/feature/basic-infomation/select-time-range';
import { Button, FormattedMessageSpan, GridFormCollapse, GridInput, Space } from '@common/components';
import {
    LayoutProComponentRefAttributesType,
    LayoutTabsPro,
    LayoutTabsProFormProps,
    LayoutTabsProProps,
} from '@common/components/feature';
import { Checkbox, Form, GridTableColumnsType, GridTableComponentRefAttributesType } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import React, { FC, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { postTraderBalanceList } from '@common/services/api/admin/mods/basic';
import { pick } from 'lodash';
import { TABLE_BG } from '@/common';
import { get as _get } from 'lodash';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
import SelectChooseCustomerGrade from '@/components/feature/select-customer/select-choose-customer-grade';
import SelectChooseCustomerType from '@/components/feature/select-customer/select-choose-customer-type';
import SelectChooseCustomerTag from '@/components/feature/select-customer/select-choose-customer-tag';
import SelectChooseCustomerRegion from '@/components/feature/select-customer/select-choose-customer-region';
import { useBoolean } from 'ahooks';
import defs from '@common/services/api/admin/api';
import ModalBalanceAdjustment from './components/modal-balance-adjustment';
import ModalBatchOption from '../../collection-management/document-settlement-query/components/modal-batch-option';
import ModalPayOption from '../../collection-management/document-settlement-query/components/modal-pay-option';

/**
 * 往来余额
 * @returns
 */
const BalanceTransactions: FC = () => {
    const intl = useIntl();

    const [form] = Form.useForm();

    // 应收
    const [receivableOpen, { setTrue: setReceivableTrue, setFalse: setReceivableFalse }] = useBoolean(false);

    // 应付
    const [copeOpen, { setTrue: setCopeTrue, setFalse: setCopeFalse }] = useBoolean(false);
    //收款
    const [receiveMoney, { setTrue: setReceiveMoneyTrue, setFalse: setReceiveMoneyFalse }] = useBoolean(false);
    const [payOpen, { setTrue: setPayOpenTrue, setFalse: setPayOpenFalse }] = useBoolean(false);
    const currentItemData = useRef(null);

    const [currentData, setCurrentData] =
        useState<defs.admin.Km_Business_Services_Basic_Response_TraderBalanceListResponse__Item>(null);

    const layoutProRef = useRef<LayoutProComponentRefAttributesType>(null);

    const tableRef = useRef<GridTableComponentRefAttributesType>(null);

    const formProps = (id: string): LayoutTabsProFormProps => {
        return {
            fieldMeta: [
                { name: 'timeRange', label: '时间', displayLabel: '时间' },
                { name: 'traderName', label: '往来单位', displayLabel: '往来单位' },
                { name: 'clerkIds', label: '所属业务员', displayLabel: '所属业务员' },
                { name: 'levelIds', label: '客户等级', displayLabel: '客户等级' },
                { name: 'typeIds', label: '客户类型', displayLabel: '客户类型' },
                { name: 'tagIds', label: '客户标签', displayLabel: '客户标签' },
                { name: 'areaIds', label: '客户区域', displayLabel: '客户区域' },
                { name: 'showAll', label: '显示全部单位', displayLabel: '显示全部单位' },
            ],
            items: [
                {
                    name: 'timeRange',
                    render(options) {
                        return (
                            <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                                <SelectTimeRange />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'traderName',
                    render(options) {
                        return (
                            <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                                <GridInput placeholder={intl.formatMessage({ id: '往来单位编号/名称/账号' })} />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'clerkIds',
                    render(options) {
                        return (
                            <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                                <SelectUser mode="multiple" placeholder={intl.formatMessage({ id: '全部' })} />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'levelIds',
                    render(options) {
                        return (
                            <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                                <SelectChooseCustomerGrade
                                    mode="multiple"
                                    placeholder={intl.formatMessage({ id: '全部' })}
                                />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'typeIds',
                    render(options) {
                        return (
                            <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                                <SelectChooseCustomerType
                                    mode="multiple"
                                    placeholder={intl.formatMessage({ id: '全部' })}
                                />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'tagIds',
                    render(options) {
                        return (
                            <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                                <SelectChooseCustomerTag
                                    mode="multiple"
                                    placeholder={intl.formatMessage({ id: '全部' })}
                                />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'areaIds',
                    render(options) {
                        return (
                            <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                                <SelectChooseCustomerRegion multiple placeholder={intl.formatMessage({ id: '全部' })} />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'showAll',
                    render(options) {
                        return (
                            <GridFormCollapse.Item
                                label={options.displayLabel}
                                name={options.name}
                                key={options.name}
                                valuePropName="checked"
                            >
                                <Checkbox />
                            </GridFormCollapse.Item>
                        );
                    },
                },
            ],
            id: id,
            form: form,
            defaultCollapsed: true,
            initialValues: { timeRange: getDateTimeRange(3) },
        };
    };

    const columns: Array<GridTableColumnsType> = [
        {
            dataIndex: 'options',
            key: 'options',
            align: 'center',
            summary: false,
            renderFormatter: (options) => {
                return (
                    <Space.Group>
                        <FormattedMessageSpan
                            type="link"
                            id="收款"
                            onClick={() => {
                                currentItemData.current = options.item;
                                setReceiveMoneyTrue();
                            }}
                        />
                        <FormattedMessageSpan
                            type="link"
                            id="付款"
                            onClick={() => {
                                currentItemData.current = options.item;
                                setPayOpenTrue();
                            }}
                        />
                        <FormattedMessageSpan type="link" id="往来对账(TODO)" />
                        <FormattedMessageSpan
                            type="link"
                            id="应收调整"
                            onClick={() => {
                                setReceivableTrue();
                                setCurrentData(options.item);
                            }}
                        />
                        <FormattedMessageSpan
                            type="link"
                            id="应付调整"
                            onClick={() => {
                                setCopeTrue();
                                setCurrentData(options.item);
                            }}
                        />
                    </Space.Group>
                );
            },
        },
        {
            dataIndex: 'name',
            key: 'name',
            align: 'center',
            sorter: true,
            summary: false,
            renderFormatter: (options) => {
                return <Button type="link">{options.value}</Button>;
            },
        },
        {
            dataIndex: 'sn',
            key: 'sn',
            align: 'center',
            sorter: true,
            summary: false,
        },
        {
            dataIndex: 'initiatorUserName',
            key: 'initiatorUserName',
            align: 'center',
            sorter: true,
            summary: false,
        },
        {
            dataIndex: 'clerkName',
            key: 'clerkName',
            align: 'center',
            sorter: true,
            summary: false,
        },
        {
            dataIndex: 'levelName',
            key: 'levelName',
            align: 'center',
            sorter: true,
            summary: false,
        },
        {
            dataIndex: 'typeName',
            key: 'typeName',
            align: 'center',
            sorter: true,
            summary: false,
        },
        {
            dataIndex: 'tags',
            key: 'tags',
            align: 'center',
            sorter: true,
            summary: false,
            renderFormatter(options) {
                return options.item?.tags?.map((item: any) => item.name).join(',');
            },
        },
        {
            dataIndex: 'areaName',
            key: 'areaName',
            align: 'center',
            sorter: true,
            summary: false,
        },
        {
            dataIndex: 'unpaidAmount',
            key: 'unpaidAmount',
            align: 'center',
            sorter: true,
        },
        {
            dataIndex: 'receivableBalance',
            key: 'receivableBalance',
            align: 'center',
            sorter: true,
            renderFormatter: (options) => {
                return <Button type="link">{options.value}</Button>;
            },
        },
        {
            dataIndex: 'preBalance',
            key: 'preBalance',
            align: 'center',
            sorter: true,
            renderFormatter: (options) => {
                return <Button type="link">{options.value}</Button>;
            },
        },
        {
            dataIndex: 'payableBalance',
            key: 'payableBalance',
            align: 'center',
            sorter: true,
            renderFormatter: (options) => {
                return <Button type="link">{options.value}</Button>;
            },
        },
        {
            dataIndex: 'tradeBalance',
            key: 'tradeBalance',
            align: 'center',
            sorter: true,
            renderFormatter: (options) => {
                return <Button type="link">{options.value}</Button>;
            },
        },
        {
            dataIndex: 'TODO1',
            key: 'TODO1',
            align: 'center',
            sorter: true,
        },
        {
            dataIndex: 'TODO2',
            key: 'TODO2',
            align: 'center',
            sorter: true,
        },
        {
            dataIndex: 'creditQuota',
            key: 'creditQuota',
            align: 'center',
            sorter: true,
        },
        {
            dataIndex: 'usableQuota',
            key: 'usableQuota',
            align: 'center',
            sorter: true,
        },
        {
            dataIndex: 'contactName',
            key: 'contactName',
            align: 'center',
            sorter: true,
            summary: false,
        },
        {
            dataIndex: 'contactPhone',
            key: 'contactPhone',
            align: 'center',
            sorter: true,
            summary: false,
        },
    ];

    const tableProps = (id: string): LayoutTabsProProps['items'][number]['tableProps'] => {
        return {
            id,
            columns: columns,
            rowKey: 'id',
            columnsMeta: [
                {
                    title: '操作',
                    displayTitle: '操作',
                    key: 'options',
                },
                {
                    title: '往来单位名称',
                    displayTitle: '往来单位名称',
                    key: 'name',
                },
                {
                    title: '往来单位编号',
                    displayTitle: '往来单位编号',
                    key: 'sn',
                },
                {
                    title: '登录账号',
                    displayTitle: '登录账号',
                    key: 'initiatorUserName',
                },
                {
                    title: '业务员',
                    displayTitle: '业务员',
                    key: 'clerkName',
                },
                {
                    title: '客户等级',
                    displayTitle: '客户等级',
                    key: 'levelName',
                },
                {
                    title: '客户类型',
                    displayTitle: '客户类型',
                    key: 'typeName',
                },
                {
                    title: '客户标签',
                    displayTitle: '客户标签',
                    key: 'tags',
                },
                {
                    title: '客户区域',
                    displayTitle: '客户区域',
                    key: 'areaName',
                },
                {
                    title: '订货未付',
                    displayTitle: '订货未付',
                    key: 'unpaidAmount',
                },
                {
                    title: '应收余额',
                    displayTitle: '应收余额',
                    key: 'receivableBalance',
                },
                {
                    title: '预收余额',
                    displayTitle: '预收余额',
                    key: 'preBalance',
                },
                {
                    title: '应付余额',
                    displayTitle: '应付余额',
                    key: 'payableBalance',
                },
                {
                    title: '往来余额',
                    displayTitle: '往来余额',
                    key: 'tradeBalance',
                },
                {
                    title: '本期应收',
                    displayTitle: '本期应收',
                    key: 'TODO1',
                },
                {
                    title: '本期回款',
                    displayTitle: '本期回款',
                    key: 'TODO2',
                },
                {
                    title: '授信额度',
                    displayTitle: '授信额度',
                    key: 'creditQuota',
                },
                {
                    title: '可用额度',
                    displayTitle: '可用额度',
                    key: 'usableQuota',
                },
                {
                    title: '联系人',
                    displayTitle: '联系人',
                    key: 'contactName',
                },
                {
                    title: '联系电话',
                    displayTitle: '联系电话',
                    key: 'contactPhone',
                },
            ],
            defaultSortOrder: 'descend',
            defaultSortOrderField: 'Id',
            sortDirections: ['ascend', 'descend', 'ascend'],
            request: async (params) => {
                try {
                    const jsonData: any = {
                        ...(pick(params, ['pager', 'sorter']) as Required<Pick<typeof params, 'pager' | 'sorter'>>),
                        ...params.form,
                        startTime: params?.form?.timeRange?.[0]
                            ? dayjs(params?.form?.timeRange[0]).unix()
                            : getDateTimeRange(3)[0].unix(),
                        endTime: params?.form?.timeRange?.[1]
                            ? dayjs(params?.form?.timeRange[1]).unix()
                            : getDateTimeRange(3)[1].unix(),
                    };
                    if (jsonData.timeRange) {
                        delete jsonData.timeRange;
                    }
                    const { list, pager } = await postTraderBalanceList(jsonData);
                    return {
                        data: list,
                        total: pager.totals,
                    };
                } catch (err) {
                    console.log(err);

                    return {
                        data: [],
                        total: 0,
                    };
                }
            },
            rowSelection: {
                type: 'checkbox',
            },
            readonly: true,
            resizable: true,
            pagination: {},
            disabledBackground: TABLE_BG,
            ref: tableRef,
            summaryTotal(dataSource, dataIndex) {
                return {
                    value: dataIndex
                        ? dataSource.reduce((pre, cur) => pre + (Number(_get(cur, dataIndex)) || 0), 0)
                        : '',
                };
            },
        };
    };

    const actions = () => {
        return (
            <>
                <Button>
                    <FormattedMessage id="导出Excel" />
                </Button>
            </>
        );
    };

    return (
        <>
            <LayoutTabsPro
                items={[
                    {
                        label: <FormattedMessage id={'往来余额'} />,
                        key: '1',
                        action: actions(),
                        formProps: formProps('01'),
                        tableProps: tableProps('01'),
                        ref: layoutProRef,
                    },
                ]}
            />
            {/* 应收调整 */}
            <ModalBalanceAdjustment
                dataSource={currentData}
                type={803}
                open={receivableOpen}
                onCancel={() => setReceivableFalse()}
                onOk={() => {
                    setReceivableFalse();
                    layoutProRef.current?.refreshRequest();
                }}
            />
            {/* 应付调整 */}
            <ModalBalanceAdjustment
                dataSource={currentData}
                type={804}
                open={copeOpen}
                onCancel={() => setCopeFalse()}
                onOk={() => {
                    setCopeFalse();
                    layoutProRef.current?.refreshRequest();
                }}
            />
            {/* 收款 */}
            <ModalBatchOption
                open={receiveMoney}
                dataSource={currentItemData.current}
                onCancel={setReceiveMoneyFalse}
                type={'collection'}
            />
            {/* 付款 */}
            <ModalPayOption
                open={payOpen}
                dataSource={currentItemData.current}
                onCancel={setPayOpenFalse}
                type={'pay'}
            />
        </>
    );
};

BalanceTransactions.displayName = 'BalanceTransactions';

export default BalanceTransactions;

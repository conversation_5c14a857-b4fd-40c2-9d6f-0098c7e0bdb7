import { TABLE_BG } from '@/common';
import { BILL_STATUS } from '@/common/message';
import { SelectTimeRange } from '@/components/feature/basic-infomation/select-time-range';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
import ModalConfirm from '@/components/feature/modal-confirm';
import { enumToSelectOptions } from '@/utils/enum-to-select-options';
import { Button, FormattedMessageSpan, GridFormCollapse, GridInput, GridSelect, Space, Text } from '@common/components';
import { LayoutProComponentRefAttributesType, LayoutTabsPro, LayoutTabsProProps } from '@common/components/feature';
import { postFeeIncomeDelete, postFeeIncomeList } from '@common/services/api/admin/mods/finance';
import { Form, GridTableColumnsType, message } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useTags } from '@weway/ui';
import { useBoolean } from 'ahooks';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import React, { FC, useRef } from 'react';

const OtherExpenseBill: FC = () => {
    const intl = useIntl();
    const { navigate } = useTags();
    const layoutProRef = useRef<LayoutProComponentRefAttributesType>(null);
    // const tableRef = useRef<GridTableComponentRefAttributesType>(null);
    const [deleteOpen, { setTrue: setDeleteOpenTrue, setFalse: setDeleteOpenFalse }] = useBoolean(false);
    const confirmId = useRef(undefined);
    const columns: Array<GridTableColumnsType> = [
        {
            // title: <FormattedMessage id={'操作'} />,
            dataIndex: 'option',
            key: 'option',
            alwaysControl: true,
            ellipsis: true,
            editableIcon: false,
            readonly: false,
            renderControl: (option) => (
                <Space.Group>
                    <FormattedMessageSpan id={'打印'} type="link" />
                    <FormattedMessageSpan
                        id={'复制'}
                        type="link"
                        onClick={async () => {
                            try {
                                navigate('/finance/revenue-expense/other-expense-bill-order', {
                                    state: {
                                        id: option.item.id,
                                        isCopy: true,
                                    },
                                });
                            } catch (error) {}
                        }}
                    />
                    <FormattedMessageSpan
                        id={'删除'}
                        type="link"
                        onClick={() => {
                            confirmId.current = option.item.id;
                            setDeleteOpenTrue();
                        }}
                    />
                </Space.Group>
            ),
            fixed: 'first',
            align: 'center',
        },
        {
            // title: <FormattedMessage id={'下单时间'} />,
            dataIndex: 'businessDate',
            key: 'businessDate',
            align: 'center',
            sorter: true,
            width: 160,
            renderFormatter(options) {
                return dayjs.unix(options.item.businessDate).format('YYYY-MM-DD HH:mm:ss');
            },
        },
        {
            // title: <FormattedMessage id={'单据编号'} />,
            dataIndex: 'businessNumber',
            key: 'businessNumber',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return (
                    <FormattedMessageSpan
                        type="link"
                        id={(options.value as string) || '--'}
                        onClick={() => {
                            if (options.item.status === 0) {
                                navigate('/finance/revenue-expense/other-expense-bill-order', {
                                    state: { id: options.item.id },
                                });
                            } else {
                                navigate('/finance/revenue-expense/other-expense-bill-order-detail', {
                                    state: { id: options.item.id },
                                });
                            }
                        }}
                    />
                );
            },
        },
        {
            // title: <FormattedMessage id={'单据状态'} />,
            dataIndex: 'status',
            key: 'status',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return (
                    <FormattedMessageSpan
                        type={options.value === 0 ? 'primary' : 'text'}
                        id={BILL_STATUS[options.value as number] ?? '--'}
                    />
                );
            },
        },
        {
            // title: <FormattedMessage id={'往来单位'} />,
            dataIndex: 'traderName',
            key: 'traderName',
            align: 'center',
            sorter: true,
        },
        {
            // title: <FormattedMessage id={'经手人'} />,
            dataIndex: 'handlerName',
            key: 'handlerName',
            align: 'center',
            sorter: true,
        },
        {
            // title: <FormattedMessage id={'金额'} />,
            dataIndex: 'amount',
            key: 'amount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return <Text type="danger">{options.item.amount}</Text>;
            },
        },
        {
            // title: <FormattedMessage id={'收款金额'} />,
            dataIndex: 'paidAmount',
            key: 'paidAmount',
            align: 'center',
            sorter: true,
        },
        {
            // title: <FormattedMessage id={'未收金额'} />,
            dataIndex: 'unpaidAmount',
            key: 'unpaidAmount',
            align: 'center',
            sorter: true,
        },

        {
            // title: <FormattedMessage id={'单据说明'} />,
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
        },
    ];
    const [form] = Form.useForm();
    const formProps = (id: string) => {
        return {
            fieldMeta: [
                { name: 'timeRange', label: '时间', displayLabel: '时间' },
                { name: 'businessNumber', label: '单据编号', displayLabel: '单据编号' },
                { name: 'status', label: '单据状态', displayLabel: '单据状态' },
                { name: 'traderName', label: '往来单位', displayLabel: '往来单位' },
                { name: 'handlerId', label: '经手人', displayLabel: '经手人' },

                { name: 'brief', label: '单据说明', displayLabel: '单据说明' },
            ],
            items: [
                {
                    name: 'timeRange',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <SelectTimeRange />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'businessNumber',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridInput placeholder={intl.formatMessage({ id: '单据编号' })} />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'status',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridSelect
                                    options={enumToSelectOptions(BILL_STATUS)}
                                    placeholder={intl.formatMessage({ id: '全部' })}
                                />
                            </GridFormCollapse.Item>
                        );
                    },
                },

                {
                    name: 'handlerId',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <SelectUser mode="multiple" />
                            </GridFormCollapse.Item>
                        );
                    },
                },

                {
                    name: 'traderName',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridInput placeholder={intl.formatMessage({ id: '往来单位名称/编号' })} />
                            </GridFormCollapse.Item>
                        );
                    },
                },

                {
                    name: 'brief',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridInput />
                            </GridFormCollapse.Item>
                        );
                    },
                },
            ],
            id: id,
            form: form,
            expandIcon: false,
            defaultCollapsed: true,
            initialValues: { timeRange: [dayjs().startOf('month'), dayjs().endOf('month')] },
        };
    };
    const tableProps = (): LayoutTabsProProps['items'][number]['tableProps'] => {
        return {
            columns: columns,
            rowKey: 'id',
            columnsMeta: [
                {
                    title: '操作',
                    displayTitle: '操作',
                    key: 'option',
                },
                {
                    title: '下单时间',
                    displayTitle: '下单时间',
                    key: 'businessDate',
                },
                {
                    title: '单据编号',
                    displayTitle: '单据编号',
                    key: 'businessNumber',
                },
                {
                    title: '单据状态',
                    displayTitle: '单据状态',
                    key: 'status',
                },
                {
                    title: '往来单位',
                    displayTitle: '往来单位',
                    key: 'traderName',
                },
                {
                    title: '经手人',
                    displayTitle: '经手人',
                    key: 'handlerName',
                },
                {
                    title: '金额',
                    displayTitle: '金额',
                    key: 'amount',
                },
                {
                    title: '收款金额',
                    displayTitle: '收款金额',
                    key: 'paidAmount',
                },
                {
                    title: '未收金额',
                    displayTitle: '未收金额',
                    key: 'unpaidAmount',
                },
                {
                    title: '单据说明',
                    displayTitle: '单据说明',
                    key: 'remark',
                },
            ],
            defaultSortOrder: 'ascend',
            defaultSortOrderField: 'businessDate',
            sortDirections: ['ascend', 'descend', 'ascend'],
            request: async (params) => {
                try {
                    const { list, pager } = await postFeeIncomeList({
                        ...(pick(params, ['pager', 'sorter']) as Required<Pick<typeof params, 'pager' | 'sorter'>>),
                        ...params.form,
                        beginTime: params?.form?.timeRange?.[0]
                            ? (dayjs(params?.form?.timeRange[0]).unix() as any)
                            : dayjs().startOf('month').unix(),
                        endTime: params?.form?.timeRange?.[1]
                            ? (dayjs(params?.form?.timeRange[1]).unix() as any)
                            : dayjs().endOf('month').unix(),
                        type: 802,
                    });

                    return {
                        data: list,
                        total: pager.totals,
                    };
                } catch (err) {
                    console.log(err);

                    return {
                        data: [],
                        total: 0,
                    };
                }
            },

            rowSelection: {
                type: 'checkbox',
            },
            readonly: true,
            resizable: true,
            pagination: {},
            disabledBackground: TABLE_BG,
        };
    };
    const actions = () => {
        return (
            <>
                <Button type="primary" onClick={handleAdd}>
                    <FormattedMessage id="新增(N)" />
                </Button>
                <Button>
                    <FormattedMessage id="批量打印" />
                </Button>
                <Button>
                    <FormattedMessage id="导出Excel" />
                </Button>
            </>
        );
    };

    const handleAdd = async () => {
        navigate('/finance/revenue-expense/other-expense-bill-order');
    };
    //删除
    const handleDelete = async (id: number) => {
        try {
            layoutProRef.current.runLoading();
            setDeleteOpenFalse();
            await postFeeIncomeDelete({ id: id }, intl);
            await layoutProRef.current.refreshRequest();
            layoutProRef.current.stopLoading();

            message.success(intl.formatMessage({ id: '删除成功' }));
        } catch (error) {
            setDeleteOpenFalse();
            layoutProRef.current.stopLoading();
        }
    };
    return (
        <>
            {/* 删除 */}
            <ModalConfirm
                type="BILL_DELETE"
                onOk={() => {
                    handleDelete(confirmId.current);
                }}
                open={deleteOpen}
                onCancel={setDeleteOpenFalse}
            />
            <LayoutTabsPro
                items={[
                    {
                        label: <FormattedMessage id={'其他收入单列表'} />,
                        key: '1',
                        ref: layoutProRef,
                        action: actions(),
                        formProps: formProps('01'),
                        tableProps: tableProps(),
                    },
                ]}
            />
        </>
    );
};

OtherExpenseBill.displayName = 'OtherExpenseBill';

export default OtherExpenseBill;

import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Space, FormatTextAuto, FormattedMessageSpan } from '@common/components';
import styles from './index.module.less';
import { GridInput } from '@weway/beacon';
import { useFormatMessage } from '@common/hooks/useFormatMessage';
import { GRID_FORM_MODAL_WIDTH } from '@/common';
import dayjs from 'dayjs';
type FooterProps = {
    onFinish: (footData: any) => void;
    datas?: any;
};
const index = forwardRef((props: FooterProps, ref: any) => {
    const { onFinish, datas } = props;
    const [remarks, setRemarks] = useState('');
    useEffect(() => {
        if (datas?.remark) {
            setRemarks(datas.remark);
        } else {
            setRemarks('');
        }
    }, [datas]);
    useImperativeHandle(ref, () => ({
        submit() {
            onFinish({
                remarks: remarks,
            });
        },
    }));
    return (
        <div className={styles.footer}>
            {datas?.id && (
                <div className={styles.footerLeft}>
                    <Space direction="vertical">
                        <FormatTextAuto.Locales
                            id="制单：{username} {date}"
                            value={{
                                username: datas?.createdUserName ?? '',
                                date: dayjs.unix(datas?.creationTime).format('YYYY-MM-DD HH:mm:ss'),
                            }}
                            defaultMessage={
                                '制单：' +
                                (datas?.createdUserName ?? '') +
                                ' ' +
                                dayjs.unix(datas?.creationTime).format('YYYY-MM-DD HH:mm:ss')
                            }
                        />
                        <FormatTextAuto.Locales
                            id="提交：{username} {date}"
                            value={{
                                username: datas?.createdUserName ?? '',
                                date: dayjs.unix(datas?.creationTime).format('YYYY-MM-DD HH:mm:ss'),
                            }}
                            defaultMessage={
                                '提交：' +
                                (datas?.createdUserName ?? '') +
                                ' ' +
                                dayjs.unix(datas?.creationTime).format('YYYY-MM-DD HH:mm:ss')
                            }
                        />
                    </Space>
                </div>
            )}

            <div className={styles.footerRight}>
                <Space>
                    <FormatTextAuto.Locales id="整单备注：" />
                    <GridInput.TextArea
                        value={remarks}
                        style={{ width: GRID_FORM_MODAL_WIDTH }}
                        onChange={(e) => {
                            setRemarks(e.target.value);
                        }}
                        placeholder={useFormatMessage({ id: '请输入备注' })}
                        rows={4}
                        disabled
                    />
                </Space>
            </div>
            {datas?.id && (
                <div>
                    <Space size="large">
                        <Space>
                            <FormatTextAuto.Locales id="总行数:" />
                            <span style={{ color: 'red' }}>{datas?.itemCount || 0}</span>
                        </Space>
                        <Space>
                            <FormatTextAuto.Locales id="总数量:" />
                            <span style={{ color: 'red' }}>{datas?.totalQty || 0}</span>
                            <FormattedMessageSpan type="subText" id={datas?.sizeUnitQtyText || '件'} />
                        </Space>
                        <Space>
                            <FormatTextAuto.Locales id="金额:" />
                            <span style={{ color: 'red' }}>{datas?.totalQty || 0}</span>
                        </Space>
                    </Space>
                </div>
            )}
        </div>
    );
});
export default index;
index.displayName = 'index';

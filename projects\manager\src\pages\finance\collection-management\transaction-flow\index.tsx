import { DataSummary, LayoutTabsPro } from '@common/components/feature';
import {
    Button,
    FormattedMessageSpan,
    FormatTextAuto,
    GridFormCollapse,
    GridInput,
    Tag,
    Text,
} from '@common/components';
import { GridTableColumnsType } from '@weway/beacon';
import { GridSelect } from '@common/components';
import React, { FC, useCallback, useState } from 'react';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { SelectTimeRange } from '@/components/feature/basic-infomation/select-time-range';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
import { enumToSelectOptions } from '@/utils/enum-to-select-options';
import { PAY_WAY, PAYMENT_STATUS, SETTLE_STATUS, TRADER_SETTLE_STATUS } from '@/common/message';
import { pick } from 'lodash';
import { postTradeRequestList } from '@common/services/api/admin/mods/finance';
import dayjs from 'dayjs';
import { get as _get } from 'lodash';
import { TABLE_BG } from '@/common';
import { useReceiveAccount } from '@/hooks/useReceiveAccount';
import { SelectCustomer } from '@/components/feature/basic-infomation/select-customer';

const TransactionFlow: FC = () => {
    const intl = useIntl();
    const [dataSummary, setDataSummary] = useState<any>({});
    const { selectOptions: selectOptionsReceiveAccountData } = useReceiveAccount();
    const columns: Array<GridTableColumnsType> = [
        {
            // title: <FormattedMessage id={'发起时间'} />,
            dataIndex: 'creationTime',
            key: 'creationTime',
            align: 'center',
            summary: false,
            sorter: true,
            width: 160,
            renderFormatter(options) {
                return dayjs.unix(options.value as number).format('YYYY-MM-DD HH:mm:ss');
            },
        },

        {
            // title: <FormattedMessage id={'交易流水'} />,
            dataIndex: 'requestNumber',
            key: 'requestNumber',
            align: 'center',
            summary: false,
            sorter: true,
            width: 160,
            renderFormatter(options) {
                return <FormattedMessageSpan type="link" id={(options.value as string) || '--'} />;
            },
        },
        {
            // title: <FormattedMessage id={'支付状态'} />,
            dataIndex: 'tradeStatus',
            key: 'tradeStatus',
            align: 'center',
            summary: false,
            renderFormatter(options) {
                switch (options.value as number) {
                    case 0:
                        return <Tag type="NoPay" />;
                    case 1:
                        return <Tag type="Prepaid" />;
                    case 2:
                        return <Tag type="PartialPay" />;
                    default:
                        return <Tag type="Unknown" />;
                }
            },
        },
        {
            // title: <FormattedMessage id={'客户名称'} />,
            dataIndex: 'customerName',
            key: 'customerName',
            align: 'center',
            summary: false,
            sorter: true,
        },
        {
            // title: <FormattedMessage id={'客户编号'} />,
            dataIndex: 'customerSn',
            key: 'customerSn',
            align: 'center',
            summary: false,
            sorter: true,
        },
        {
            // title: <FormattedMessage id={'登录账号'} />,
            dataIndex: 'customerUserName',
            key: 'customerUserName',
            align: 'center',
            summary: false,
        },
        {
            // title: <FormattedMessage id={'发起单号'} />,
            dataIndex: 'requestNumber',
            key: 'requestNumber',
            align: 'center',
            summary: false,
            sorter: true,
            renderFormatter(options) {
                return (
                    <FormattedMessageSpan type="link" id={options.item?.requestNumber?.[0]?.requestNumber || '--'} />
                );
            },
        },
        {
            // title: <FormattedMessage id={'发起类型'} />,
            dataIndex: 'tradeType',
            key: 'tradeType',
            align: 'center',
            summary: false,
        },
        {
            // title: <FormattedMessage id={'经手人'} />,
            dataIndex: 'handlerName',
            key: 'handlerName',
            align: 'center',
            summary: false,
            sorter: true,
            renderFormatter(options) {
                return <FormattedMessageSpan type="primary" id={(options.value as string) || '--'} />;
            },
        },
        {
            // title: <FormattedMessage id={'交易金额'} />,
            dataIndex: 'tradeAmount',
            key: 'tradeAmount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return <Text type="danger">{options.value || '--'}</Text>;
            },
        },

        {
            // title: <FormattedMessage id={'手续费'} />,
            dataIndex: 'serviceFee',
            key: 'serviceFee',
            align: 'center',
            sorter: true,
        },
        {
            // title: <FormattedMessage id={'实收金额'} />,
            dataIndex: 'actualAmount',
            key: 'actualAmount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return <Text type="danger">{options.value || '--'}</Text>;
            },
        },
        {
            // title: <FormattedMessage id={'收款时间'} />,
            dataIndex: 'succeedTime',
            key: 'succeedTime',
            align: 'center',
            summary: false,
            sorter: true,
            renderFormatter(options) {
                return (
                    (options.value as number) > 0 && dayjs.unix(options.value as number).format('YYYY-MM-DD HH:mm:ss')
                );
            },
        },
        {
            // title: <FormattedMessage id={'冻结金额'} />,
            dataIndex: 'freezeAmount',
            key: 'freezeAmount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return <Text type="danger">{options.value || '--'}</Text>;
            },
        },
        {
            // title: <FormattedMessage id={'结算状态'} />,
            dataIndex: 'settleStatus',
            key: 'settleStatus',
            align: 'center',
            summary: false,
            renderFormatter(options) {
                return <FormattedMessage id={TRADER_SETTLE_STATUS[options.value as number] || '未知'} />;
            },
        },
        {
            // title: <FormattedMessage id={'结算时间'} />,
            dataIndex: 'settleTime',
            key: 'settleTime',
            align: 'center',
            summary: false,
            sorter: true,
            renderFormatter(options) {
                return (
                    (options.value as number) > 0 && dayjs.unix(options.value as number).format('YYYY-MM-DD HH:mm:ss')
                );
            },
        },
        {
            // title: <FormattedMessage id={'支付类型'} />,
            dataIndex: 'typeId',
            key: 'typeId',
            align: 'center',
            summary: false,
            renderFormatter(options) {
                return <FormattedMessage id={PAY_WAY[options.value as number] || '--'} />;
            },
        },
        {
            // title: <FormattedMessage id={'交易账户'} />,
            dataIndex: 'kindName',
            key: 'kindName',
            align: 'center',
            summary: false,
        },

        {
            // title: <FormattedMessage id={'第三方交易凭证号'} />,
            dataIndex: 'tradeVoucherNumber',
            key: 'tradeVoucherNumber',
            align: 'center',
            summary: false,
        },
    ];
    /**
     * 表头与表体之间的数据汇总
     * @param tabIndex
     * @returns
     */
    const headerLastChildren = useCallback(
        (tabIndex: string) => {
            const dataSource = dataSummary?.[tabIndex];
            if (!dataSource) {
                return null;
            }
            return (
                <DataSummary
                    styles={{ margin: '10px 0' }}
                    columns={[
                        {
                            title: intl.formatMessage({ id: '待支付余额' }),
                            key: 'totalQty',
                        },
                        {
                            title: intl.formatMessage({ id: '本期已收' }),
                            key: 'costAmount',
                        },
                    ]}
                    dataSource={dataSource}
                />
            );
        },
        [dataSummary],
    );
    return (
        <LayoutTabsPro
            items={[
                {
                    label: <FormattedMessage id={'在线交易流水'} />,
                    key: '1',
                    action: (
                        <>
                            <Button>
                                <FormatTextAuto.Locales id="导出Excel" />
                            </Button>
                        </>
                    ),
                    formProps: {
                        fieldMeta: [
                            { name: 'timeRange', label: '', displayLabel: '' },
                            { name: 'keyword', label: '交易流水', displayLabel: '交易流水' },
                            { name: 'serialNumber', label: '交易凭证号', displayLabel: '交易凭证号' },
                            { name: 'customerIds', label: '客户名称', displayLabel: '客户名称' },
                            { name: 'handlerIds', label: '经手人', displayLabel: '经手人' },
                            { name: 'tradeStatus', label: '支付状态', displayLabel: '支付状态' },
                            { name: 'businessType', label: '发起类型', displayLabel: '发起类型' },
                            { name: 'requestNumber', label: '发起单号', displayLabel: '发起单号' },
                            { name: 'receiveIds', label: '收款账户', displayLabel: '收款账户' },
                            { name: 'settleStatus', label: '结算状态', displayLabel: '结算状态' },
                            { name: 'receiveTimeRange', label: '收款时间', displayLabel: '收款时间' },
                            { name: 'settleTimeRange', label: '结算时间', displayLabel: '结算时间' },
                        ],
                        items: [
                            {
                                name: 'timeRange',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <SelectTimeRange />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                            {
                                name: 'keyword',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <GridInput />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                            {
                                name: 'serialNumber',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <GridInput placeholder={intl.formatMessage({ id: '第三方凭证号' })} />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                            {
                                name: 'customerIds',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <SelectCustomer.Mutiple />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                            {
                                name: 'handlerIds',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <SelectUser
                                                mode="multiple"
                                                placeholder={intl.formatMessage({ id: '全部' })}
                                            />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                            {
                                name: 'tradeStatus',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <GridSelect
                                                placeholder={intl.formatMessage({ id: '全部' })}
                                                options={enumToSelectOptions(PAYMENT_STATUS)}
                                            />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                            {
                                name: 'businessType',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <GridSelect
                                                placeholder={intl.formatMessage({ id: '全部' })}
                                                options={enumToSelectOptions(PAYMENT_STATUS)}
                                            />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                            {
                                name: 'requestNumber',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <GridInput />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                            {
                                name: 'receiveIds',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <GridSelect options={selectOptionsReceiveAccountData} mode="multiple" />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                            {
                                name: 'settleStatus',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <GridSelect
                                                placeholder={intl.formatMessage({ id: '全部' })}
                                                options={enumToSelectOptions(SETTLE_STATUS)}
                                            />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                            {
                                name: 'receiveTimeRange',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <SelectTimeRange />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                            {
                                name: 'settleTimeRange',
                                render(options) {
                                    return (
                                        <GridFormCollapse.Item
                                            label={options.label}
                                            name={options.name}
                                            key={options.name}
                                        >
                                            <SelectTimeRange />
                                        </GridFormCollapse.Item>
                                    );
                                },
                            },
                        ],
                        id: '01',
                        defaultCollapsed: true,
                    },
                    headerLastChildren: headerLastChildren('01'),
                    tableProps: {
                        id: '01',
                        columns: columns,
                        rowKey: 'id',
                        columnsMeta: [
                            {
                                title: '发起时间',
                                displayTitle: '发起时间',
                                key: 'creationTime',
                            },
                            {
                                title: '交易流水',
                                displayTitle: '交易流水',
                                key: 'requestNumber',
                            },
                            {
                                title: '支付状态',
                                displayTitle: '支付状态',
                                key: 'tradeStatus',
                            },
                            {
                                title: '客户名称',
                                displayTitle: '客户名称',
                                key: 'customerName',
                            },
                            {
                                title: '客户编号',
                                displayTitle: '客户编号',
                                key: 'customerSn',
                            },
                            {
                                title: '登录账号',
                                displayTitle: '登录账号',
                                key: 'customerUserName',
                            },
                            {
                                title: '发起单号',
                                displayTitle: '发起单号',
                                key: 'requestNumber',
                            },
                            {
                                title: '发起类型',
                                displayTitle: '发起类型',
                                key: 'tradeType',
                            },
                            {
                                title: '经手人',
                                displayTitle: '经手人',
                                key: 'handlerName',
                            },
                            {
                                title: '交易金额',
                                displayTitle: '交易金额',
                                key: 'tradeAmount',
                            },
                            {
                                title: '手续费',
                                displayTitle: '手续费',
                                key: 'serviceFee',
                            },
                            {
                                title: '实收金额',
                                displayTitle: '实收金额',
                                key: 'actualAmount',
                            },
                            {
                                title: '收款时间',
                                displayTitle: '收款时间',
                                key: 'succeedTime',
                            },
                            {
                                title: '冻结金额',
                                displayTitle: '冻结金额',
                                key: 'freezeAmount',
                            },
                            {
                                title: '结算状态',
                                displayTitle: '结算状态',
                                key: 'settleStatus',
                            },
                            {
                                title: '结算时间',
                                displayTitle: '结算时间',
                                key: 'settleTime',
                            },
                            {
                                title: '支付类型',
                                displayTitle: '支付类型',
                                key: 'typeId',
                            },
                            {
                                title: '交易账户',
                                displayTitle: '交易账户',
                                key: 'kindName',
                            },
                            {
                                title: '第三方交易凭证号',
                                displayTitle: '第三方交易凭证号',
                                key: 'tradeVoucherNumber',
                            },
                        ],
                        defaultSortOrder: 'descend',
                        defaultSortOrderField: 'id',
                        sortDirections: ['ascend', 'descend', 'ascend'],
                        request: async (params) => {
                            try {
                                const { list, pager } = await postTradeRequestList({
                                    ...(pick(params, ['pager', 'sorter']) as Required<
                                        Pick<typeof params, 'pager' | 'sorter'>
                                    >),
                                    ...params.form,
                                    // startTime: params?.form?.timeRange?.[0]
                                    //     ? (dayjs(params?.form?.timeRange[0]).unix() as any)
                                    //     : dayjs().startOf('month').unix(),
                                    // endTime: params?.form?.timeRange?.[1]
                                    //     ? (dayjs(params?.form?.timeRange[1]).unix() as any)
                                    //     : dayjs().endOf('month').unix(),
                                });
                                setDataSummary((prevState: any) => ({
                                    ...prevState,
                                    ['01']: {
                                        totalQty: 0,
                                        costAmount: 0,
                                    },
                                }));
                                return {
                                    data: list,
                                    total: pager.totals,
                                };
                            } catch (err) {
                                return {
                                    data: [],
                                    total: 0,
                                };
                            }
                        },

                        rowSelection: {
                            type: 'checkbox',
                        },
                        readonly: true,
                        resizable: true,
                        pagination: {},
                        disabledBackground: TABLE_BG,
                        summaryTotal(dataSource, dataIndex) {
                            return {
                                value: dataIndex
                                    ? dataSource.reduce((pre, cur) => pre + (Number(_get(cur, dataIndex)) || 0), 0)
                                    : '',
                            };
                        },
                    },
                },
            ]}
        />
    );
};

TransactionFlow.displayName = 'TransactionFlow';

export default TransactionFlow;

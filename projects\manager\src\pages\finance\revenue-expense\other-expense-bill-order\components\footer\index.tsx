import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Space, FormatTextAuto, GridSelect, FormattedMessageSpan, Text } from '@common/components';
import styles from './index.module.less';
import { GridInput, GridInputNumber } from '@weway/beacon';
import { FormattedMessage } from '@weway/i18n';
import { useReceiveAccount } from '@/hooks/useReceiveAccount';
type FooterProps = {
    onFinish: (footData: any) => void;
    datas?: any;
};
const index = forwardRef((props: FooterProps, ref: any) => {
    const { onFinish, datas } = props;
    const [returnType1, setReturnType1] = useState(null);
    const [money1, setMoney1] = useState(null);
    const [returnType2, setReturnType2] = useState(null);
    const [money2, setMoney2] = useState(null);
    const [remark, setRemark] = useState('');
    const { selectOptions: selectOptionsAccountData } = useReceiveAccount();
    useEffect(() => {
        setRemark(datas?.remark || '');
        setReturnType1(datas?.payments?.[0]?.method || null);
        setMoney1(datas?.payments?.[0]?.amount || null);
        setReturnType2(datas?.payments?.[1]?.method || null);
        setMoney2(datas?.payments?.[1]?.amount || null);
    }, [datas]);
    useImperativeHandle(ref, () => ({
        submit() {
            onFinish({
                remark: remark,
                payments: [
                    {
                        id: returnType1,
                        value: money1,
                    },
                    {
                        id: returnType2,
                        value: money2,
                    },
                ].filter((item) => item.id),
            });
        },
    }));
    return (
        <div className={styles.footerContainer}>
            <div className={styles.footerBox1}>
                <Space>
                    <FormatTextAuto.Locales id={'制单:'} />
                    <FormatTextAuto.Locales id={'张三'} />
                    <FormatTextAuto.Locales id={'2024-01-26  12:15:16'} />
                </Space>
                <Space>
                    <FormatTextAuto.Locales id={'提交:'} />
                    <FormatTextAuto.Locales id={'张三'} />
                    <FormatTextAuto.Locales id={'2024-01-26  12:15:16'} />
                </Space>
            </div>
            <div className={styles.footerBox1}>
                <Space align="start">
                    <FormatTextAuto.Locales id={'整单备注:'} />
                    <GridInput.TextArea
                        rows={4}
                        style={{ width: '240px' }}
                        value={remark}
                        onChange={(e) => setRemark(e.target.value)}
                    />
                </Space>
            </div>
            <div className={styles.footerBox2}>
                <Space direction="vertical">
                    <Space>
                        <FormattedMessage id="付款1" />
                        <GridSelect
                            style={{ width: '100px' }}
                            options={selectOptionsAccountData}
                            value={returnType1}
                            onChange={(e) => setReturnType1(e)}
                        />
                        <GridInputNumber
                            addonBefore={<FormattedMessage id="金额" />}
                            style={{ width: '160px' }}
                            value={money1}
                            onChange={(e) => setMoney1(e)}
                        />
                    </Space>
                    <Space>
                        <FormattedMessage id="付款2" />
                        <GridSelect
                            style={{ width: '100px' }}
                            options={selectOptionsAccountData}
                            value={returnType2}
                            onChange={(e) => setReturnType2(e)}
                        />
                        <GridInputNumber
                            addonBefore={<FormattedMessage id="金额" />}
                            style={{ width: '160px' }}
                            value={money2}
                            onChange={(e) => setMoney2(e)}
                        />
                    </Space>
                </Space>
                <div className={styles.line}></div>
                <div style={{ color: 'red' }}>
                    <Space>
                        <Space>
                            <FormattedMessageSpan id="金额" />
                            <Text>{datas?.totalAmount || 0}</Text>
                        </Space>
                        <Space>
                            <FormattedMessageSpan id="欠款" />
                            <Text>{datas?.unPaidAmount || 0}</Text>
                        </Space>
                    </Space>
                </div>
            </div>
        </div>
    );
});
export default index;
index.displayName = 'index';

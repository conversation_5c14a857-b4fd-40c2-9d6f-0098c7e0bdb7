import { ConfigGridTable, ConfigGridTableColumnsType } from '@common/components/feature';
import { GRID_TABLE_ACTIONS_ADD, GRID_TABLE_ACTIONS_DELETE } from '@weway/beacon';
import { UseGridTableRefAttributesReturn } from '@weway/beacon/dist/grid-table/hooks/data';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { uniqueId } from 'lodash';
import { postSubjectSelectorList } from '@common/services/api/admin/mods/finance';
import { tableSummaryTotal } from '@/utils';

type DocumentTableProps = {
    onFinish: (data: any) => void;
    datas?: any[];
    status?: number;
};
const DocumentTable = forwardRef((props: DocumentTableProps, ref) => {
    const { onFinish, datas } = props;
    const tableRef = useRef<UseGridTableRefAttributesReturn>(null);
    const [dataSource, setDataSource] = useState<any>(
        new Array(1).fill(undefined).map((_) => ({
            id: 'uuid' + uniqueId(),
        })),
    );
    const columns: Array<ConfigGridTableColumnsType<any>> = [
        {
            title: '收入编号',
            dataIndex: 'code',
            key: 'code',
            alwaysControl: true,
            ellipsis: false,
            align: 'center',
            type: 'action-input',
            readonly: false,
            summary: false,
            actionInputProps(options) {
                return {
                    selectProps: {
                        onChange(value) {
                            const newData = tableRef.current.getDataSource();
                            newData[options.rowIndex] = {
                                ...value,
                                id: newData[options.rowIndex]?.id || value.code,
                            };

                            setDataSource([...newData]);
                        },
                        style: {
                            width: 400,
                            height: 300,
                        },
                        columns: [
                            {
                                title: '收入名称',
                                dataIndex: 'name',
                            },
                            {
                                title: '编号',
                                dataIndex: 'code',
                            },
                        ],
                        rowKey: 'id',
                        request: async (search = '') => {
                            try {
                                const { list } = await postSubjectSelectorList({
                                    keyword: search,
                                    pager: {
                                        size: 999,
                                        index: 1,
                                    },
                                    sorter: [
                                        {
                                            member: 'code',
                                            mode: 0,
                                        },
                                    ],
                                    subjectCode: '6051',
                                });
                                return list;
                            } catch (error) {
                                return [];
                            }
                        },
                    },
                };
            },
        },
        {
            title: '收入名称',
            dataIndex: 'name',
            key: 'name',
            summary: false,
            alwaysControl: true,
            ellipsis: false,
            align: 'center',
            type: 'action-input',
            readonly: false,
            actionInputProps(options) {
                return {
                    selectProps: {
                        onChange(value) {
                            const newData = tableRef.current.getDataSource();
                            newData[options.rowIndex] = {
                                ...value,
                                id: newData[options.rowIndex]?.id || value.code,
                            };

                            setDataSource([...newData]);
                        },
                        style: {
                            width: 400,
                            height: 300,
                        },
                        columns: [
                            {
                                title: '收入名称',
                                dataIndex: 'name',
                            },
                            {
                                title: '编号',
                                dataIndex: 'code',
                            },
                        ],
                        rowKey: 'id',
                        request: async (search = '') => {
                            try {
                                const { list } = await postSubjectSelectorList({
                                    keyword: search,
                                    pager: {
                                        size: 999,
                                        index: 1,
                                    },
                                    sorter: [
                                        {
                                            member: 'code',
                                            mode: 0,
                                        },
                                    ],
                                    subjectCode: '6051',
                                });
                                return list;
                            } catch (error) {
                                return [];
                            }
                        },
                    },
                };
            },
        },
        {
            title: '路径',
            dataIndex: 'path',
            key: 'path',
            summary: false,
            align: 'center',
        },
        {
            title: '金额',
            dataIndex: 'amount',
            key: 'amount',
            align: 'center',
            readonly: false,
            async onBlur(options) {
                const value = options.value;
                const valueNumber: number = parseFloat(value as string);
                if (isNaN(valueNumber)) {
                    return;
                }

                const newData = tableRef.current.getDataSource();
                newData[options.rowIndex].amount = valueNumber;
                setDataSource(newData);
            },
        },

        {
            title: '明细备注',
            dataIndex: 'remark',
            key: 'remark',
            summary: false,
            align: 'center',
            readonly: false,
            async onBlur(options) {
                const value = options.value;

                const newData = tableRef.current.getDataSource();
                newData[options.rowIndex].remark = value;
                setDataSource(newData);
            },
        },
    ];

    useEffect(() => {
        if (datas && datas.length > 0) {
            // const length = datas.length;
            // let list: any = [];
            // if (length < 10) {
            //     list = new Array(10 - length).fill(undefined).map((_) => ({
            //         id: 'uuid' + uniqueId(),
            //     }));
            // }
            setDataSource([...datas]);
        } else {
            setDataSource(
                new Array(1).fill(undefined).map((_) => ({
                    id: 'uuid' + uniqueId(),
                })),
            );
        }
    }, [datas]);
    useImperativeHandle(ref, () => ({
        submit() {
            setDataSource(tableRef.current.getDataSource());
            onFinish(tableRef.current.getDataSource());
        },
    }));

    return (
        <>
            <ConfigGridTable
                ref={tableRef}
                configId=""
                resizable
                columns={columns}
                rowKey={'code'}
                dataSource={dataSource}
                editableIcon={false}
                readonly={true}
                summaryTotal={tableSummaryTotal}
                scroll={{ y: 550 }}
                action={{
                    options: [GRID_TABLE_ACTIONS_ADD, GRID_TABLE_ACTIONS_DELETE],
                    width: 80,
                }}
            />
        </>
    );
});
DocumentTable.displayName = 'DocumentTable';
export default DocumentTable;

import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { DocumentDetailProps } from './data';
import { GridForm } from '@weway/beacon';
import { FormatTextAuto, Space, Tag, Text } from '@common/components';
import DocumentTable from './DocumentTable';
import dayjs from 'dayjs';
import { DELIVERY_MODE } from '@/common/message';
import { ReadOnlyDataCard } from '@/components/feature/read-only-data-card';
import Footer from '../../other-warehouse-receipts/components/footer';
/**
 * 单据详情
 * @returns
 */
const DocumentDetail = forwardRef((props: DocumentDetailProps, ref: any) => {
    const { datas } = props;
    const tabRef = useRef<any>();
    const footerRef = useRef<any>();
    const [form] = GridForm.useForm();
    const submitData: any = {
        formData: {},
        tableData: [],
        footerData: {},
    };

    useEffect(() => {
        console.log('详情', datas);
        if (datas?.id) {
            form.setFieldsValue(datas);
        } else {
            form.resetFields();
            form.setFieldValue('businessNumber', datas?.businessNumber); // 单据编号
        }
    }, [datas]);

    useImperativeHandle(ref, () => ({
        async submit() {
            form.submit();
        },
    }));
    return (
        <>
            <ReadOnlyDataCard
                dataSource={{
                    businessNumber: datas?.businessNumber,
                    businessDate: dayjs.unix(datas?.businessDate).format('YYYY-MM-DD'),
                    traderName: datas?.traderName,
                    handlerName: datas?.handlerName,
                    departmentName: datas?.departmentName,
                    depotName: datas?.depotName,
                    deliveryMode: DELIVERY_MODE[datas?.deliveryMode],
                    deliveryName: datas?.deliveryName,
                    deliveryPhone: datas?.deliveryPhone,
                    brief: datas?.brief,
                    packQty: datas?.packQty,
                }}
                columns={[
                    {
                        title: <FormatTextAuto.Locales id="单据编号" />,
                        key: 'businessNumber',
                        render: (option) => {
                            const { value } = option;
                            return (
                                <Space>
                                    <Text type="primary">{value}</Text>
                                    <Tag type="Submited" />
                                </Space>
                            );
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="单据日期" />,
                        key: 'businessDate',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="往来单位" />,
                        key: 'traderName',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="经手人" />,
                        key: 'handlerName',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="部门" />,
                        key: 'departmentName',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="仓库" />,
                        key: 'depotName',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },

                    {
                        title: <FormatTextAuto.Locales id="承运方式" />,
                        key: 'deliveryMode',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="承运单位" />,
                        key: 'deliveryName',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="承运电话" />,
                        key: 'deliveryPhone',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },

                    {
                        title: <FormatTextAuto.Locales id="单据说明" />,
                        key: 'brief',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },

                    {
                        title: <FormatTextAuto.Locales id="打包件数" />,
                        key: 'packQty',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                ]}
            />
            <div style={{ marginTop: 10, marginBottom: 10 }}>
                <DocumentTable
                    ref={tabRef}
                    datas={datas?.items}
                    id={datas?.id}
                    status={datas?.status}
                    onFinish={(tableData) => {
                        submitData.tableData = tableData;
                    }}
                />
            </div>
            <Footer
                ref={footerRef}
                datas={datas}
                type="out"
                onFinish={(values: any) => {
                    submitData.footerData = values;
                }}
                tableData={datas?.items}
            />
        </>
    );
});

export default DocumentDetail;
DocumentDetail.displayName = 'DocumentDetail';

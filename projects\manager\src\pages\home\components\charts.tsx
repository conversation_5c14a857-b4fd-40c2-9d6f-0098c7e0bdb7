import React, { RefObject, useEffect, useRef } from 'react';
import { DualAxes } from '@antv/g2plot';
import { Row, Col, Button, TimePicker, Space } from '@weway/beacon';
import switchIcon from '@/assets/image/home/<USER>';
import styles from './index.module.less';
import { FormattedMessage } from '@weway/i18n';
const data = [
    { amount: 73250, date: '2023-09-13', quantity: 19 },
    { amount: 73250, date: '2023-09-13', quantity: 19 },
    { amount: 49850, date: '2023-09-14', quantity: 27 },
    { amount: 91150, date: '2023-09-15', quantity: 23 },
    { amount: 73250, date: '2023-09-16', quantity: 1 },
    { amount: 2000, date: '2023-09-17', quantity: 2 },
    { amount: 0, date: '2023-09-18', quantity: 28 },
    { amount: 53500, date: '2023-09-19', quantity: 2 },
];

export default function Charts() {
    const chartRef: RefObject<DualAxes | null> = useRef(null);
    useEffect(() => {
        const line = new DualAxes('container', {
            data: [data, data],
            xField: 'date',
            yField: ['amount', 'quantity'],
            meta: {
                amount: {
                    alias: '金额',
                },
                quantity: {
                    alias: '数量',
                },
            },
            legend: {
                position: 'bottom',
            },
            geometryOptions: [
                {
                    geometry: 'line',
                    color: '#F48A0E',
                    smooth: true,
                    point: {
                        shape: 'circle',
                        size: 4,
                        style: {
                            opacity: 0.5,
                            stroke: '#5AD8A6',
                            fill: '#fff',
                        },
                    },
                    lineStyle: {
                        lineWidth: 2,
                        opacity: 0.8,
                    },
                },
                {
                    geometry: 'line',
                    color: '#00A0E7',
                    smooth: true,
                    point: {
                        shape: 'circle',
                        size: 4,
                        style: {
                            opacity: 0.5,
                            stroke: '#5AD8A6',
                            fill: '#fff',
                        },
                    },
                    lineStyle: {
                        lineWidth: 2,
                        opacity: 0.8,
                    },
                },
            ],

            // 修改 x 轴样式
            xAxis: {
                tickCount: 10, // x 轴刻度数量
                label: {
                    style: {
                        fill: '#999', // x 轴标签颜色
                    },
                },
            },

            // 修改 y 轴样式
            yAxis: [
                {
                    tickCount: 8, // y 轴刻度数量
                    label: {
                        style: {
                            fill: '#999', // y 轴标签颜色
                        },
                    },
                    grid: {
                        line: {
                            style: {
                                stroke: '#F0F0F0', // y 轴网格线颜色
                            },
                        },
                    },
                },
            ],

            // 添加 tooltip
            // tooltip: {
            //   showMarkers: false, // 不显示 tooltip marker 的圆点
            //   showTitle: false, // 不显示 tooltip 标题
            //   customContent: (title, items) => {
            //     const [item1, item2] = items; // 获取两组数据的 tooltip 数据

            //     const container = document.createElement('div');

            //     // 创建 HTML 结构来显示 tooltip，根据实际需求修改
            //     container.innerHTML = `
            //       <div style="padding: 8px;">
            //         <div>${title}</div>
            //         <div style="color: ${item1.color};">${item1.value}</div>
            //         <div style="color: ${item2.color};">${item2.value}</div>
            //       </div>
            //     `;
            //     return container;
            //   },
            // },
        });
        line.render();
    }, []);
    return (
        <>
            <Row className={styles.chartsSearch}>
                <Col style={{ whiteSpace: 'nowrap' }}>
                    <Space>
                        <Button>
                            <FormattedMessage id="近七日" />
                        </Button>
                        <Button>
                            <FormattedMessage id="近30日" />
                        </Button>
                    </Space>
                </Col>
                <Col push={1}>
                    <TimePicker.RangePicker />
                </Col>
                <Col span={10} style={{ textAlign: 'right' }}>
                    <img src={switchIcon} alt="" />
                </Col>
            </Row>
            <div id="container" ref={chartRef} />
        </>
    );
}

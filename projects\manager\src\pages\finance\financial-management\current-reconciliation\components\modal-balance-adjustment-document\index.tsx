import { Button, FormattedMessageSpan, FormatTextAuto, Modal } from '@common/components';
import { ModalBalanceAdjustmentDocumentProps } from './data';
import { Input, Space, Spin } from '@weway/beacon';
import React, { useRef } from 'react';
import { useIntl } from '@weway/i18n';
import { useBoolean, useRequest } from 'ahooks';
import { DEBOUNCE_TIME } from '@/common';
import { getAdaptGet } from '@common/services/api/admin/mods/finance';
import { ReadOnlyDataCard } from '@/components/feature/read-only-data-card';
import { ConfigGridTable } from '@common/components/feature';
import { UseGridTableRefAttributesReturn } from '@weway/beacon/dist/grid-table/hooks/data';
import { get as _get } from 'lodash';
/**
 * 余额调整单据详情
 * @returns
 */
const ModalBalanceAdjustmentDocument = (props: ModalBalanceAdjustmentDocumentProps) => {
    const { open, text, type, dataSource, onCancel, onOk } = props;

    const intl = useIntl();

    const tableRef = useRef<UseGridTableRefAttributesReturn>(null);

    const billTypeText = {
        803: intl.formatMessage({ id: '应收调整单' }),
        804: intl.formatMessage({ id: '应付调整单' }),
        606: intl.formatMessage({ id: '资金调整单' }),
    };

    const [loading, { setTrue: setLoadingTrue, setFalse: setLoadingFalse }] = useBoolean(false);

    const { data: adaptData } = useRequest(
        async () => {
            if (!dataSource?.id) return;
            const response = await getAdaptGet(dataSource?.id, intl);
            return response;
        },
        { manual: true, throttleWait: DEBOUNCE_TIME },
    );

    const handleOk = async () => {
        try {
            setLoadingTrue();
            onOk?.();
            setLoadingFalse();
        } catch (error) {
            setLoadingFalse();
            console.log(error);
        }
    };

    const handleCancel = () => {
        onCancel?.();
    };

    return (
        <Modal
            text={text}
            open={open}
            title={billTypeText[type]}
            width={'1180'}
            onOk={handleOk}
            onCancel={handleCancel}
            footer={
                <Space>
                    <Button
                        loading={loading}
                        onClick={() => {
                            handleCancel();
                        }}
                    >
                        <FormattedMessageSpan id="取消（Esc）" />
                    </Button>
                </Space>
            }
            destroyOnClose
        >
            <Spin spinning={loading}>
                <ReadOnlyDataCard
                    style={{ marginBottom: 10 }}
                    dataSource={adaptData}
                    columns={[
                        {
                            title: <FormatTextAuto.Locales id="单据编号" />,
                            key: 'businessNumber',
                            span: 8,
                            render: (option) => {
                                return (
                                    <div style={{ display: 'flex', alignItems: 'center' }}>
                                        <span style={{ color: 'rgb(242,152,35)', marginRight: 10 }}>
                                            {option.value}
                                        </span>
                                        <div
                                            style={{
                                                backgroundColor: '#F84532',
                                                color: '#FFF',
                                                padding: '1px 10px',
                                                borderRadius: 5,
                                            }}
                                        >
                                            <FormattedMessageSpan id="调增" />
                                        </div>
                                    </div>
                                );
                            },
                        },
                        {
                            title: <FormatTextAuto.Locales id="调整时间" />,
                            key: 'businessDate',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="客户名称" />,
                            key: 'customerId',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="调整金额" />,
                            key: 'adjustAmount',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="经手人" />,
                            key: 'handlerName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="部门" />,
                            key: 'departmentName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="备注" />,
                            key: 'remark',
                            span: 8,
                        },
                    ]}
                />
                <ConfigGridTable
                    ref={tableRef}
                    configId=""
                    resizable
                    columns={[
                        {
                            title: '操作',
                            dataIndex: 'options',
                            key: 'options',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作时间',
                            dataIndex: 'creationTime',
                            key: 'creationTime',
                            align: 'center',
                            type: 'date-picker',
                            summary: false,
                        },
                        {
                            title: '操作人',
                            dataIndex: 'createdName',
                            key: 'createdName',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作ip',
                            dataIndex: 'ipAddress',
                            key: 'ipAddress',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作类型',
                            dataIndex: 'operationType',
                            key: 'operationType',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作日志',
                            dataIndex: 'content',
                            key: 'content',
                            align: 'center',
                            summary: false,
                        },
                    ]}
                    rowKey={'code'}
                    dataSource={adaptData?.logs || []}
                    editableIcon={false}
                    readonly={true}
                    summaryTotal={(dataSource, dataIndex) => {
                        return {
                            value: dataIndex
                                ? dataSource.reduce((pre, cur) => pre + (Number(_get(cur, dataIndex)) || 0), 0)
                                : '',
                        };
                    }}
                    scroll={{ y: 300 }}
                />
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span>
                        <FormattedMessageSpan id="追加日志" />：
                    </span>
                    <Input.TextArea style={{ width: 400, marginRight: 10 }} />
                    <Button type="primary">
                        <FormattedMessageSpan id="确认追加" />
                    </Button>
                </div>
            </Spin>
        </Modal>
    );
};
ModalBalanceAdjustmentDocument.displayName = 'ModalBalanceAdjustmentDocument';
export default ModalBalanceAdjustmentDocument;

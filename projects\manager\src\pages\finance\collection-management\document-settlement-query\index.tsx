import { TABLE_BG } from '@/common';
import { BUSINESS_TYPE } from '@/common/message';
import { enumToSelectOptions } from '@/utils/enum-to-select-options';
import { Button, FormattedMessageSpan, GridFormCollapse, GridInput, GridSelect, Space } from '@common/components';
import {
    AuthComponent,
    LayoutProComponentRefAttributesType,
    LayoutTabsPro,
    LayoutTabsProProps,
} from '@common/components/feature';
import { postPendingstoragePage, postStoragePage } from '@common/services/api/admin/mods/inventory';
import { GridTableComponentRefAttributesType } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { message } from 'antd';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import React, { FC, useRef, useState } from 'react';
import ModalBatchOption from './components/modal-batch-option';
import { useBoolean } from 'ahooks';
import defs from '@common/services/api/admin/api';
import { SelectTimeRange } from '@/components/feature/basic-infomation/select-time-range';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
import ModalPaymentDetail from './components/modal-payment-details';
import ModalBatchPayment from './components/modal-batch-payment';

/**
 * 单据结算查询
 * @returns
 */
const DocumentSettlementQuery: FC = () => {
    const intl = useIntl();
    const layoutProRefOne = useRef<LayoutProComponentRefAttributesType>(null);
    const layoutProRefTwo = useRef<LayoutProComponentRefAttributesType>(null);
    const tableOneRef = useRef<GridTableComponentRefAttributesType>(null);
    const tableTwoRef = useRef<GridTableComponentRefAttributesType>(null);

    const [selectItems, setSelectItems] = useState<
        defs.admin.Km_Business_Work_Application_Admin_Inventory_PendingStorage_PendingStoragePage__Response__DataItem[]
    >([]);

    const [batchOpen, { setTrue: setBatchOpenTrue, setFalse: setBatchOpenFalse }] = useBoolean(false);
    const [paymentDetailOpen, { setFalse: setPaymentDetailFalse }] = useBoolean(false);
    const [batchPaymentOpen, { setTrue: setBatchPaymentTrue, setFalse: setBatchPaymentFalse }] = useBoolean(false);

    /**
     * 批量发货
     * @returns
     */
    const handleBatchShip = () => {
        const data = tableOneRef.current?.getSelectItems();
        if (data?.length === 0) {
            message.error(intl.formatMessage({ id: '请选择单据' }));
            return;
        }
        setSelectItems(data);
        setBatchOpenTrue();
    };

    const actions = (type: number) => {
        return (
            <>
                {type === 1 && (
                    <AuthComponent id="">
                        <Button type="primary" onClick={handleBatchShip}>
                            <FormattedMessage id="批量收款" />
                        </Button>
                    </AuthComponent>
                )}
                {type === 2 && (
                    <AuthComponent id="">
                        <Button type="primary" onClick={setBatchPaymentTrue}>
                            <FormattedMessage id="批量付款" />
                        </Button>
                    </AuthComponent>
                )}
                <AuthComponent id="">
                    <Button
                        onClick={() => {
                            const data =
                                type === 1
                                    ? tableOneRef.current.getSelectItems()
                                    : tableTwoRef.current.getSelectItems();
                            if (data?.length) {
                            } else {
                                message.error(intl.formatMessage({ id: '请选择' }));
                            }
                        }}
                    >
                        <FormattedMessage id={'批量打印'} />
                    </Button>
                </AuthComponent>
                <AuthComponent id="">
                    <Button>
                        <FormattedMessage id="导出Excel" />
                    </Button>
                </AuthComponent>
            </>
        );
    };

    const formProps = (formid: string, type: number) => {
        console.log(type);
        const items = [
            {
                name: 'timeRange',
                render(options: any) {
                    return (
                        <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                            <SelectTimeRange />
                        </GridFormCollapse.Item>
                    );
                },
            },
            {
                name: 'businessNumber',
                render(options: any) {
                    return (
                        <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                            <GridInput placeholder={intl.formatMessage({ id: '往来单位' })} />
                        </GridFormCollapse.Item>
                    );
                },
            },
            {
                name: 'businessType',
                render(options: any) {
                    return (
                        <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                            <GridSelect options={enumToSelectOptions(BUSINESS_TYPE, true)} />
                        </GridFormCollapse.Item>
                    );
                },
            },
            {
                name: 'depotId',
                render(options: any) {
                    return (
                        <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                            <SelectUser />
                        </GridFormCollapse.Item>
                    );
                },
            },
            {
                name: 'correspondent',
                render(options: any) {
                    return (
                        <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                            <GridInput placeholder={intl.formatMessage({ id: '单据类型' })} />
                        </GridFormCollapse.Item>
                    );
                },
            },
            {
                name: 'handlerId',
                render(options: any) {
                    return (
                        <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                            <GridInput placeholder={intl.formatMessage({ id: '单据编号' })} />
                        </GridFormCollapse.Item>
                    );
                },
            },
            {
                name: 'product',
                render(options: any) {
                    return (
                        <GridFormCollapse.Item label={options.displayLabel} name={options.name} key={options.name}>
                            <GridInput placeholder={intl.formatMessage({ id: '商品名称/编号/条码' })} />
                        </GridFormCollapse.Item>
                    );
                },
            },
        ];
        const fieldMeta = [
            { name: 'timeRange', label: '', displayLabel: '' },
            { name: 'businessNumber', label: '往来单位', displayLabel: '往来单位' },
            { name: 'businessType', label: '收款状态', displayLabel: '收款状态' },
            { name: 'depotId', label: '经手人', displayLabel: '经手人' },
            { name: 'correspondent', label: '单据类型', displayLabel: '单据类型' },
            { name: 'handlerId', label: '单据编号', displayLabel: '单据编号' },
            { name: 'product', label: '商品', displayLabel: '商品' },
        ];
        return {
            fieldMeta: fieldMeta,
            items: items,
            id: formid,
            defaultCollapsed: true,
            initialValues: {},
        };
    };

    const tableProps = (id: string, tableIndex: number): LayoutTabsProProps['items'][number]['tableProps'] => {
        return {
            id,
            columns: [
                {
                    dataIndex: 'businessDate',
                    key: 'businessDate',
                    align: 'center',
                    sorter: true,
                    width: 180,
                    renderFormatter(options) {
                        return (
                            <Button type="link">
                                {dayjs.unix(options.value as number).format('YYYY-MM-DD HH:mm:ss')}
                            </Button>
                        );
                    },
                },
                {
                    dataIndex: 'businessNumber',
                    key: 'businessNumber',
                    align: 'center',
                    sorter: true,
                    renderFormatter(options) {
                        return (
                            <FormattedMessageSpan
                                type="link"
                                id={options.value as string}
                                onClick={() => {
                                    console.log(options);
                                }}
                            />
                        );
                    },
                },
                {
                    dataIndex: 'depotName',
                    key: 'depotName',
                    align: 'center',
                    sorter: true,
                },
                {
                    dataIndex: 'traderName',
                    key: 'traderName',
                    align: 'center',
                    sorter: true,
                    renderFormatter(options) {
                        return <Button type="link">{options.value}</Button>;
                    },
                },
                {
                    dataIndex: 'handlerName',
                    key: 'handlerName',
                    align: 'center',
                },
                {
                    dataIndex: 'itemCount',
                    key: 'itemCount',
                    align: 'center',
                    renderFormatter(options) {
                        return <FormattedMessageSpan type="primary" id={(options.value as string) || '0'} />;
                    },
                },
                {
                    dataIndex: 'totalQty',
                    key: 'totalQty',
                    align: 'center',
                    alwaysControl: true,
                    renderControl(options) {
                        return (
                            <Space direction="vertical">
                                <Button type="link">{options.item.totalQty}</Button>
                                {options.item.sizeUnitQtyText && (
                                    <Button type="link">{options.item.sizeUnitQtyText}</Button>
                                )}
                            </Space>
                        );
                    },
                },
                {
                    dataIndex: 'totalWeight',
                    key: 'totalWeight',
                    align: 'center',
                },
                {
                    dataIndex: 'fromType',
                    key: 'fromType',
                    align: 'center',
                    renderFormatter(options) {
                        const fromType = options.item.fromType;
                        const formTypeName = BUSINESS_TYPE?.[fromType] || '未知单据';
                        return <FormattedMessageSpan type="primary" id={formTypeName} />;
                    },
                },
                {
                    dataIndex: 'deliveryMode',
                    key: 'deliveryMode',
                    align: 'center',
                },
                {
                    dataIndex: 'traderContactName',
                    key: 'traderContactName',
                    align: 'center',
                },
                {
                    dataIndex: 'traderContactPhone',
                    key: 'traderContactPhone',
                    align: 'center',
                },
                {
                    dataIndex: 'traderAddress',
                    key: 'traderAddress',
                    align: 'center',
                },
                {
                    dataIndex: 'remark',
                    key: 'remark',
                    align: 'center',
                },
            ],
            rowKey: 'id',
            ref: tableIndex === 1 ? tableOneRef : tableTwoRef,
            columnsMeta: [
                {
                    title: '日期',
                    displayTitle: '日期',
                    key: 'businessDate',
                },
                {
                    title: '单据编号',
                    displayTitle: '单据编号',
                    key: 'businessNumber',
                },
                {
                    title: '单据类型',
                    displayTitle: '单据类型',
                    key: 'depotName',
                },
                {
                    title: '往来单位名称',
                    displayTitle: '往来单位名称',
                    key: 'traderName',
                },
                {
                    title: '经手人',
                    displayTitle: '经手人',
                    key: 'handlerName',
                },
                {
                    title: '仓库',
                    displayTitle: '仓库',
                    key: 'itemCount',
                },
                {
                    title: '单据金额',
                    displayTitle: '单据金额',
                    key: 'totalQty',
                },
                {
                    title: '收款金额',
                    displayTitle: '收款金额',
                    key: 'totalWeight',
                },
                {
                    title: '未收金额',
                    displayTitle: '未收金额',
                    key: 'fromType',
                },
                {
                    title: '收款状态',
                    displayTitle: '收款状态',
                    key: 'deliveryMode',
                },
                {
                    title: '单据备注',
                    displayTitle: '单据备注',
                    key: 'traderContactName',
                },
            ],
            defaultSortOrder: 'ascend',
            defaultSortOrderField: tableIndex === 1 ? 'fromBusinessDate' : 'businessNumber',
            sortDirections: ['ascend', 'descend'],
            request: async (params: any) => {
                try {
                    const jsonData = {
                        ...(pick(params, ['pager', 'sorter']) as Required<Pick<typeof params, 'pager' | 'sorter'>>),
                        ...params.form,
                        beginTime: params.form?.timeRange?.[0] ? dayjs(params.form.timeRange[0]).unix() : null,
                        endTime: params.form?.timeRange?.[1] ? dayjs(params.form.timeRange[1]).unix() : null,
                        sorter: [],
                        type: 502,
                    };
                    if (jsonData.timeRange) {
                        delete jsonData.timeRange;
                    }
                    if (tableIndex === 1) {
                        const { list, pager } = await postPendingstoragePage(jsonData);
                        return {
                            data: list,
                            total: pager.totals,
                        };
                    } else {
                        const { list, pager } = await postStoragePage(jsonData);
                        return {
                            data: list,
                            total: pager.totals,
                        };
                    }
                } catch (err) {
                    return {
                        data: [],
                        total: 0,
                    };
                }
            },
            rowSelection: {
                type: 'checkbox',
            },
            readonly: true,
            resizable: true,
            pagination: {},
            disabledBackground: TABLE_BG,
        };
    };

    return (
        <>
            <LayoutTabsPro
                items={[
                    {
                        label: <FormattedMessage id={'应收单据'} />,
                        key: '1',
                        ref: layoutProRefOne,
                        action: actions(1),
                        formProps: formProps('01', 1),
                        tableProps: tableProps('01', 1),
                    },
                    {
                        label: <FormattedMessage id={'应付单据'} />,
                        key: '2',
                        ref: layoutProRefTwo,
                        action: actions(2),
                        formProps: formProps('02', 2),
                        tableProps: tableProps('02', 2),
                    },
                ]}
            />
            <ModalBatchOption
                open={batchOpen}
                dataSource={selectItems}
                onCancel={() => setBatchOpenFalse()}
                onOk={() => setBatchOpenFalse()}
            />
            <ModalPaymentDetail
                open={paymentDetailOpen}
                dataSource={selectItems}
                onCancel={() => setPaymentDetailFalse()}
                onOk={() => setPaymentDetailFalse()}
            />
            {/* 付款单 */}
            <ModalBatchPayment open={batchPaymentOpen} onCancel={setBatchPaymentFalse} />
        </>
    );
};

DocumentSettlementQuery.displayName = 'DocumentSettlementQuery';

export default DocumentSettlementQuery;

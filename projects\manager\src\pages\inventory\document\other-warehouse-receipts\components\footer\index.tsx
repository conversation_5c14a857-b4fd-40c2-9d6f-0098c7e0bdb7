import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Text } from '@common/components';
import styles from './index.module.less';
import { GridInput } from '@weway/beacon';
import { FormattedMessage } from '@weway/i18n';
import dayjs from 'dayjs';
import { GRID_FORM_MODAL_WIDTH } from '@/common';
import { calculateTable } from '@/utils/calculateTable';
import MoneyDisplay from '@/components/feature/money-display';
type FooterProps = {
    onFinish: (footData: any) => void;
    datas?: any;
    tableData: Array<any>;
    type: 'out' | 'in'; //出库，入库
};
const index = forwardRef((props: FooterProps, ref: any) => {
    const { onFinish, datas, tableData, type } = props;
    const [remarks, setRemarks] = useState('');
    const userInfo: any = JSON.parse(localStorage.getItem('MANAGER_USER_AUTHORIZE'));
    useEffect(() => {
        if (datas?.remark) {
            setRemarks(datas.remark);
        } else {
            setRemarks('');
        }
    }, [datas]);
    useImperativeHandle(ref, () => ({
        submit() {
            onFinish({
                remarks: remarks,
            });
        },
    }));
    return (
        <div className={styles.footer}>
            <div className={styles.users}>
                <div className={styles.useritem}>
                    <FormattedMessage id="制单：" />
                    <Text>{userInfo?.name}</Text>
                    {datas?.id && <Text>{dayjs.unix(datas?.creationTime).format('YYYY-MM-DD HH:mm:ss')}</Text>}
                </div>
                {datas?.status === 100 && (
                    <div className={styles.useritem}>
                        <FormattedMessage id="提交：" />
                        <Text>{datas?.handlerName}</Text>
                        {datas?.id && <Text>{dayjs.unix(datas?.submittedTime).format('YYYY-MM-DD HH:mm:ss')}</Text>}
                    </div>
                )}
            </div>
            <div className={styles.remark}>
                <FormattedMessage id="整单备注：" />
                <GridInput.TextArea
                    value={remarks}
                    disabled={datas?.status === 100}
                    maxLength={500}
                    style={{ width: GRID_FORM_MODAL_WIDTH }}
                    onChange={(e) => {
                        setRemarks(e.target.value);
                    }}
                    rows={2}
                />
            </div>
            <div className={styles.totalnum}>
                <div className={styles.totalnumItem}>
                    <FormattedMessage id="总行数：" />
                    <Text type="danger" style={{ fontSize: 16 }}>
                        {calculateTable(tableData)?.totalLine}
                    </Text>
                </div>
                <div className={styles.totalnumItem}>
                    <FormattedMessage id="总数量：" />
                    <Text type="danger" style={{ fontSize: 16 }}>
                        {calculateTable(tableData)?.totalNum}
                    </Text>
                    {calculateTable(tableData)?.sizeUnitQtyText && (
                        <Text>({calculateTable(tableData)?.sizeUnitQtyText})</Text>
                    )}
                </div>
                <div className={styles.totalnumItemMoney}>
                    <FormattedMessage id="金额：" />
                    {/* 入库取折后金额 */}
                    {type === 'in' && (
                        <MoneyDisplay color="red" value={calculateTable(tableData)?.totalDiscountedAmount} />
                    )}
                    {/* 出库取成本金额 */}
                    {type === 'out' && <MoneyDisplay color="red" value={calculateTable(tableData)?.totalAmount} />}
                </div>
            </div>
        </div>
    );
});
export default index;
index.displayName = 'index';

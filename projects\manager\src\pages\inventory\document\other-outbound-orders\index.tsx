import { TABLE_BG } from '@/common';
import { BILL_STATUS, DELIVERY_MODE } from '@/common/message';
import ModalConfirm from '@/components/feature/modal-confirm';
import { AuthComponent, ProMaxTable, SelectTimeRange } from '@common/components/feature';
import { enumToSelectOptions } from '@/utils/enum-to-select-options';
import {
    Button,
    FormattedMessageSpan,
    FormatTextAuto,
    GridFormCollapse,
    GridInput,
    GridSelect,
    Space,
    Text,
} from '@common/components';
import { postOtherDelete, postOtherList } from '@common/services/api/admin/mods/inventory';
import { GridTableColumnsType, GridTableComponentRefAttributesType } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useTags } from '@weway/ui';
import { useBoolean, useRequest } from 'ahooks';
import { message } from 'antd';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import React, { FC, useRef } from 'react';
import { SelectDepot } from '@/components/feature/basic-infomation/select-depot';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';

const OtherOutboundOrders: FC = () => {
    const intl = useIntl();
    const { navigate } = useTags();
    const tableRef = useRef<GridTableComponentRefAttributesType>(null);
    const confirmId = useRef(undefined);
    const [deleteOpen, { setTrue: setDeleteOpenTrue, setFalse: setDeleteOpenFalse }] = useBoolean(false);
    const [form] = GridFormCollapse.useForm();

    const handleToAdd = async () => {
        navigate('/inventory/document/add-other-out', {
            openMore: true,
        });
    };

    const columns: Array<GridTableColumnsType> = [
        {
            title: <FormattedMessage id={'操作'} />,
            dataIndex: 'option',
            key: 'option',
            alwaysControl: true,
            readonly: false,
            editableIcon: false,
            renderControl: (option) => (
                <Space.Group>
                    <FormatTextAuto.Locales ellipsisTextProps={{ type: 'link' }} id={'打印'} />
                    <FormatTextAuto.Locales
                        ellipsisTextProps={{ type: 'link' }}
                        id={'复制'}
                        onClick={async () => {
                            navigate('/inventory/document/add-other-out', {
                                state: {
                                    id: option.item.id,
                                    isCopy: true,
                                },
                                openMore: true,
                            });
                        }}
                    />

                    {option.item.status === 0 && (
                        <AuthComponent id="17010301-8">
                            <FormatTextAuto.Locales
                                ellipsisTextProps={{ type: 'link' }}
                                id={'删除'}
                                onClick={() => {
                                    confirmId.current = option.item.id;
                                    setDeleteOpenTrue();
                                }}
                            />
                        </AuthComponent>
                    )}
                </Space.Group>
            ),
            fixed: 'first',
            align: 'center',
            width: 140,
        },
        {
            title: <FormattedMessage id={'下单时间'} />,
            dataIndex: 'creationTime',
            key: 'creationTime',
            align: 'center',
            sorter: true,
            width: 160,
            renderFormatter(options) {
                return (
                    <FormattedMessage id={dayjs.unix(options.value as number).format('YYYY-MM-DD HH:mm:ss') || '--'} />
                );
            },
        },
        {
            title: <FormattedMessage id={'单据编号'} />,
            dataIndex: 'businessNumber',
            key: 'businessNumber',
            align: 'center',
            sorter: true,
            width: 140,
            renderFormatter(options) {
                return (
                    <FormattedMessageSpan
                        type="link"
                        id={(options.value as string) || '--'}
                        onClick={() => {
                            if (options.item.status === 0) {
                                navigate('/inventory/document/edit-other-out', {
                                    state: { id: options.item.id },
                                    openMore: true,
                                });
                            } else {
                                navigate('/inventory/document/other-outbound-orders-detail', {
                                    state: { id: options.item.id },
                                    openMore: true,
                                });
                            }
                        }}
                    />
                );
            },
        },
        {
            title: <FormattedMessage id={'单据状态'} />,
            dataIndex: 'status',
            key: 'status',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return (
                    <FormattedMessageSpan
                        style={{ color: options.item.status === 0 ? 'red' : '' }}
                        id={BILL_STATUS[options.value as number] || '--'}
                    />
                );
            },
        },
        {
            title: <FormattedMessage id={'仓库'} />,
            dataIndex: 'depotName',
            key: 'depotName',
            align: 'center',
            sorter: true,
        },
        {
            title: <FormattedMessage id={'经手人'} />,
            dataIndex: 'handlerName',
            key: 'handlerName',
            align: 'center',
            sorter: true,
        },
        {
            title: <FormattedMessage id={'往来单位'} />,
            dataIndex: 'traderName',
            key: 'traderName',
            align: 'center',
            sorter: true,
        },
        {
            title: <FormattedMessage id={'行数'} />,
            dataIndex: 'itemCount',
            key: 'itemCount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return <FormattedMessageSpan type="primary" id={(options.value as string) || '--'} />;
            },
        },

        {
            title: <FormattedMessage id={'数量'} />,
            dataIndex: 'totalQty',
            key: 'totalQty',
            align: 'center',
            sorter: true,
            alwaysControl: true,
            renderControl(options) {
                return (
                    <Space direction="vertical">
                        <Text type="danger">{options.item.totalQty || '--'}</Text>
                        {options.item.sizeUnitQtyText && (
                            <FormattedMessageSpan type="subText" id={options.item.sizeUnitQtyText || '--'} />
                        )}
                    </Space>
                );
            },
        },

        {
            title: <FormattedMessage id={'成本金额'} />,
            dataIndex: 'totalCostAmount',
            key: 'totalCostAmount',
            align: 'center',
            sorter: true,
        },
        {
            title: <FormattedMessage id={'承运方式'} />,
            dataIndex: 'deliveryMode',
            key: 'deliveryMode',
            align: 'center',
            renderFormatter(options) {
                return <FormattedMessageSpan id={DELIVERY_MODE[options.value as number] || '--'} />;
            },
        },
        {
            title: <FormattedMessage id={'承运单位'} />,
            dataIndex: 'deliveryName',
            key: 'deliveryName',
            align: 'center',
        },
        {
            title: <FormattedMessage id={'承运电话'} />,
            dataIndex: 'deliveryPhone',
            key: 'deliveryPhone',
            align: 'center',
        },
        {
            title: <FormattedMessage id={'单据说明'} />,
            dataIndex: 'brief',
            key: 'brief',
            align: 'center',
        },
    ];
    const { runAsync: postDelete, loading: postDeleteLoading } = useRequest(postOtherDelete, {
        manual: true,
    });
    //删除
    const handleDelete = async (id: number) => {
        try {
            setDeleteOpenFalse();
            await postDelete({ id: id });
            await tableRef.current.reload();

            message.success(intl.formatMessage({ id: '删除成功' }));
        } catch (error) {
            setDeleteOpenFalse();
        }
    };

    return (
        <>
            {/* 删除 */}
            <ModalConfirm
                type="BILL_DELETE"
                onOk={() => {
                    handleDelete(confirmId.current);
                }}
                open={deleteOpen}
                onCancel={setDeleteOpenFalse}
            />
            <ProMaxTable
                items={[
                    {
                        label: <FormattedMessage id={'其他出库单'} />,
                        key: '1',
                        action: (
                            <>
                                <AuthComponent id="17010301-4">
                                    <Button type="primary" onClick={handleToAdd}>
                                        <FormattedMessage id="新增(N)" />
                                    </Button>
                                </AuthComponent>
                                <AuthComponent id="17010301-128">
                                    <Button
                                        onClick={() => {
                                            const data = tableRef.current.getSelectItems();
                                            if (data?.length) {
                                                setDeleteOpenTrue();
                                                confirmId.current = data.map((item) => item.id);
                                            } else {
                                                message.error(intl.formatMessage({ id: '请选择' }));
                                            }
                                        }}
                                    >
                                        <FormattedMessage id={'批量打印'} />
                                    </Button>
                                </AuthComponent>

                                <Button>
                                    <FormattedMessage id="导出Excel" />
                                </Button>
                            </>
                        ),
                        header: {
                            id: -1,

                            formProps: {
                                form,
                                expandIcon: false,
                                defaultCollapsed: true,
                                initialValues: {
                                    status: null,
                                    type: null,
                                    timeRange: [
                                        dayjs().startOf('month').startOf('day'),
                                        dayjs().endOf('month').endOf('day'),
                                    ],
                                },
                                items: [
                                    {
                                        field: 'timeRange',
                                        render(options) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.field}
                                                >
                                                    <SelectTimeRange />
                                                </GridFormCollapse.Item>
                                            );
                                        },

                                        title: '时间',
                                    },
                                    {
                                        field: 'businessNumber',
                                        render(options) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.field}
                                                >
                                                    <GridInput placeholder={intl.formatMessage({ id: '报溢单号' })} />
                                                </GridFormCollapse.Item>
                                            );
                                        },

                                        title: '单据编号',
                                    },
                                    {
                                        field: 'status',
                                        render(options) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.field}
                                                >
                                                    <GridSelect
                                                        options={enumToSelectOptions(BILL_STATUS)}
                                                        placeholder={intl.formatMessage({ id: '全部' })}
                                                    />
                                                </GridFormCollapse.Item>
                                            );
                                        },

                                        title: '单据状态',
                                    },
                                    {
                                        field: 'depotIds',
                                        render(options) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.field}
                                                >
                                                    <SelectDepot
                                                        mode="multiple"
                                                        placeholder={intl.formatMessage({ id: '全部' })}
                                                    />
                                                </GridFormCollapse.Item>
                                            );
                                        },

                                        title: '仓库',
                                    },
                                    {
                                        field: 'handlerIds',
                                        render(options) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.field}
                                                >
                                                    <SelectUser
                                                        mode="multiple"
                                                        placeholder={intl.formatMessage({ id: '全部' })}
                                                    />
                                                </GridFormCollapse.Item>
                                            );
                                        },

                                        title: '经手人',
                                    },
                                    {
                                        field: 'traderName',
                                        render(options) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.field}
                                                >
                                                    <GridInput
                                                        placeholder={intl.formatMessage({ id: '往来单位名称/编号' })}
                                                    />
                                                </GridFormCollapse.Item>
                                            );
                                        },

                                        title: '往来单位',
                                    },
                                    {
                                        field: 'deliveryMode',
                                        render(options) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.field}
                                                >
                                                    <GridSelect
                                                        options={[
                                                            {
                                                                label: intl.formatMessage({ id: '商家配送' }),
                                                                value: 3,
                                                            },
                                                            {
                                                                label: intl.formatMessage({ id: '物流快递' }),
                                                                value: 1,
                                                            },
                                                            {
                                                                label: intl.formatMessage({ id: '上门自提' }),
                                                                value: 2,
                                                            },
                                                        ]}
                                                        placeholder={intl.formatMessage({ id: '全部' })}
                                                    />
                                                </GridFormCollapse.Item>
                                            );
                                        },

                                        title: '承运方式',
                                    },
                                    {
                                        field: 'brief',
                                        render(options) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.field}
                                                >
                                                    <GridInput />
                                                </GridFormCollapse.Item>
                                            );
                                        },

                                        title: '单据说明',
                                    },
                                ],
                            },
                        },
                        content: {
                            id: -1,
                            tableProps: {
                                columns: columns,
                                rowKey: 'id',
                                loading: postDeleteLoading,
                                ref: tableRef,
                                defaultSortOrder: 'descend',
                                defaultSortOrderField: 'id',
                                sortDirections: ['ascend', 'descend', 'ascend'],
                                request: async (params: any) => {
                                    //默认显示近30天的数据
                                    let beginTime: any = dayjs().startOf('month').startOf('day').unix();
                                    let endTime: any = dayjs().endOf('month').endOf('day').unix();
                                    const { form } = params;
                                    if (Object.keys(form).length !== 0) {
                                        //当有表单数据时，取表单数据
                                        beginTime = params.form.timeRange?.[0].unix();
                                        endTime = params.form.timeRange?.[1].unix();
                                    }
                                    try {
                                        const { list, pager } = await postOtherList({
                                            ...(pick(params, ['pager', 'sorter']) as Required<
                                                Pick<typeof params, 'pager' | 'sorter'>
                                            >),
                                            ...params.form,
                                            type: 304,
                                            beginTime: beginTime,
                                            endTime: endTime,
                                        });

                                        return {
                                            data: list,
                                            total: pager.totals,
                                        };
                                    } catch (err) {
                                        return {
                                            data: [],
                                            total: 0,
                                        };
                                    }
                                },

                                rowSelection: {
                                    type: 'checkbox',
                                },
                                readonly: true,
                                resizable: true,
                                pagination: {},
                                disabledBackground: TABLE_BG,
                            },
                        },
                    },
                ]}
            />
        </>
    );
};

OtherOutboundOrders.displayName = 'OtherOutboundOrders';

export default OtherOutboundOrders;

import { TABLE_BG } from '@/common';
import { BILL_TYPE, TRADER_RECORD_TYPE } from '@/common/message';
import { SelectTimeRange } from '@/components/feature/basic-infomation/select-time-range';
import { SelectTrader } from '@/components/feature/basic-infomation/select-trader';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
import { enumToSelectOptions } from '@/utils/enum-to-select-options';
import { Button, FormattedMessageSpan, GridFormCollapse, GridInput, GridSelect, Space, Text } from '@common/components';
import { LayoutTabsPro, LayoutTabsProProps } from '@common/components/feature';
import { postTraderRecordList } from '@common/services/api/admin/mods/basic';
import { Form, GridCheckbox, GridTableColumnsType } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useBoolean } from 'ahooks';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import { get as _get } from 'lodash';
import React, { FC, useState } from 'react';
import ModalBalanceAdjustmentDocument from './components/modal-balance-adjustment-document';

const CurrentReconciliation: FC = () => {
    const intl = useIntl();

    const [billDetailOpen, { setTrue: setBillDetailOpenTrue, setFalse: setBillDetailOpenFalse }] = useBoolean(false);
    const [currentData, setCurrentData] = useState<any>(null);

    const columns: Array<GridTableColumnsType> = [
        {
            // title: <FormattedMessage id={'日期'} />,
            dataIndex: 'fromDate',
            key: 'fromDate',
            align: 'center',
            sorter: true,
            width: 160,
            summary: false,
            renderFormatter(options) {
                return dayjs.unix(options.item.fromDate).format('YYYY-MM-DD HH:mm:ss');
            },
        },
        {
            // title: <FormattedMessage id={'单据编号'} />,
            dataIndex: 'fromNumber',
            key: 'fromNumber',
            align: 'center',
            sorter: true,
            summary: false,
            width: 160,
            renderFormatter(options) {
                return (
                    <FormattedMessageSpan
                        id={options.item.fromNumber || '--'}
                        type="link"
                        onClick={() => {
                            if (
                                typeof options.value === 'string' &&
                                (options.value.includes('YS') || options.value.includes('YF'))
                            ) {
                                setCurrentData(options.item);
                                setBillDetailOpenTrue();
                            }
                        }}
                    />
                );
            },
        },
        {
            // title: <FormattedMessage id={'单据类型'} />,
            dataIndex: 'fromType',
            key: 'fromType',
            align: 'center',
            summary: false,
            renderFormatter(options) {
                return <FormattedMessage id={BILL_TYPE[options.item.fromType] || '--'} />;
            },
        },
        {
            // title: <FormattedMessage id={'业务单号'} />,
            dataIndex: 'businessNumber',
            key: 'businessNumber',
            align: 'center',
            sorter: true,
            summary: false,
            width: 160,
            renderFormatter(options) {
                return <FormattedMessageSpan id={options.item.businessNumber || '--'} type="link" onClick={() => {}} />;
            },
        },
        {
            // title: <FormattedMessage id={'经手人'} />,
            dataIndex: 'handlerName',
            key: 'handlerName',
            align: 'center',
            sorter: true,
            summary: false,
        },
        {
            // title: <FormattedMessage id={'仓库'} />,
            dataIndex: 'depotName',
            key: 'depotName',
            align: 'center',
            summary: false,
        },
        {
            // title: <FormattedMessage id={'单据金额'} />,
            dataIndex: 'fromAmount',
            key: 'fromAmount',
            align: 'center',
            sorter: true,
            summary: false,
        },
        {
            // title: <FormattedMessage id={'应收金额'} />,
            dataIndex: 'receivableAmount',
            key: 'receivableAmount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return (
                    <Text type="danger">
                        {(options.item.direction == -1 && options.item.receivableAmount > 0 ? '-' : '') +
                            options.item.receivableAmount}
                    </Text>
                );
            },
        },
        {
            // title: <FormattedMessage id={'预收金额'} />,
            dataIndex: 'preAmount',
            key: 'preAmount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return <Text type="danger">{options.item.preAmount}</Text>;
            },
        },
        {
            // title: <FormattedMessage id={'应付金额'} />,
            dataIndex: 'payableAmount',
            key: 'payableAmount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return (
                    <Text type="danger">
                        {(options.item.direction == -1 && options.item.payableAmount > 0 ? '-' : '') +
                            options.item.payableAmount}
                    </Text>
                );
            },
        },
        {
            // title: <FormattedMessage id={'单据说明'} />,
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
            summary: false,
        },
    ];
    const [form] = Form.useForm();
    const formProps = (id: string) => {
        return {
            fieldMeta: [
                { name: 'timeRange', label: '时间', displayLabel: '时间' },
                { name: 'traderName', label: '往来单位', displayLabel: '往来单位' },
                { name: 'handlerIds', label: '经手人', displayLabel: '经手人' },
                { name: 'type', label: '往来类型', displayLabel: '往来类型' },
                { name: 'businessNumber', label: '单据编号', displayLabel: '单据编号' },
                { name: 'skuName', label: '商品名称', displayLabel: '商品名称' },
                { name: 'remark', label: '单据说明', displayLabel: '单据说明' },
                { name: 'showAll', label: '显示全部单据', displayLabel: '显示全部单据' },
            ],
            items: [
                {
                    name: 'timeRange',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <SelectTimeRange />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'traderName',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <Space>
                                    <GridFormCollapse.Item name={options.name}>
                                        <SelectTrader.Single
                                            onChange={(event: any) => {
                                                form.setFieldsValue({
                                                    traderId: event.target?.dataSource?.id,
                                                });
                                            }}
                                        />
                                    </GridFormCollapse.Item>
                                    <GridFormCollapse.Item name={'traderId'} hidden />
                                </Space>
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'handlerIds',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <SelectUser mode="multiple" />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'type',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridSelect options={enumToSelectOptions(TRADER_RECORD_TYPE, true)} />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'businessNumber',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridInput placeholder={intl.formatMessage({ id: '单据编号' })} />
                            </GridFormCollapse.Item>
                        );
                    },
                },

                {
                    name: 'skuName',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridInput placeholder={intl.formatMessage({ id: '商品名称/编号' })} />
                            </GridFormCollapse.Item>
                        );
                    },
                },

                {
                    name: 'remark',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridInput />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'showAll',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridCheckbox
                                    onChange={(e) => {
                                        form.setFieldValue('showAll', e.target.checked);
                                    }}
                                />
                            </GridFormCollapse.Item>
                        );
                    },
                },
            ],
            id: id,
            form: form,
            expandIcon: false,
            defaultCollapsed: true,
            initialValues: { type: null as any, timeRange: [dayjs().startOf('month'), dayjs().endOf('month')] },
        };
    };
    const tableProps = (): LayoutTabsProProps['items'][number]['tableProps'] => {
        return {
            columns: columns,
            rowKey: 'id',
            columnsMeta: [
                {
                    title: '日期',
                    displayTitle: '日期',
                    key: 'fromDate',
                },
                {
                    title: '单据编号',
                    displayTitle: '单据编号',
                    key: 'fromNumber',
                },
                {
                    title: '单据类型',
                    displayTitle: '单据类型',
                    key: 'fromType',
                },
                {
                    title: '业务单号',
                    displayTitle: '业务单号',
                    key: 'businessNumber',
                },
                {
                    title: '经手人',
                    displayTitle: '经手人',
                    key: 'handlerName',
                },
                {
                    title: '仓库',
                    displayTitle: '仓库',
                    key: 'depotName',
                },
                {
                    title: '单据金额',
                    displayTitle: '单据金额',
                    key: 'fromAmount',
                },
                {
                    title: '应收金额',
                    displayTitle: '应收金额',
                    key: 'receivableAmount',
                },
                {
                    title: '预收金额',
                    displayTitle: '预收金额',
                    key: 'preAmount',
                },
                {
                    title: '应付金额',
                    displayTitle: '应付金额',
                    key: 'payableAmount',
                },
                {
                    title: '单据说明',
                    displayTitle: '单据说明',
                    key: 'remark',
                },
            ],
            defaultSortOrder: 'descend',
            defaultSortOrderField: 'TraderId',
            sortDirections: ['ascend', 'descend', 'ascend'],
            request: async (params) => {
                try {
                    const { list, pager } = await postTraderRecordList({
                        ...(pick(params, ['pager', 'sorter']) as Required<Pick<typeof params, 'pager' | 'sorter'>>),
                        ...params.form,
                        startTime: params?.form?.timeRange?.[0]
                            ? (dayjs(params?.form?.timeRange[0]).unix() as any)
                            : dayjs().startOf('month').unix(),
                        endTime: params?.form?.timeRange?.[1]
                            ? (dayjs(params?.form?.timeRange[1]).unix() as any)
                            : dayjs().endOf('month').unix(),
                    });

                    return {
                        data: list,
                        total: pager.totals,
                    };
                } catch (err) {
                    console.log(err);

                    return {
                        data: [],
                        total: 0,
                    };
                }
            },

            rowSelection: {
                type: 'checkbox',
            },
            readonly: true,
            resizable: true,
            pagination: {},
            disabledBackground: TABLE_BG,
            summaryTotal(dataSource, dataIndex) {
                return {
                    value: dataIndex
                        ? dataSource.reduce((pre, cur) => pre + (Number(_get(cur, dataIndex)) || 0), 0)
                        : '',
                };
            },
        };
    };
    const actions = () => {
        return (
            <>
                <Button>
                    <FormattedMessage id="导出Excel" />
                </Button>
            </>
        );
    };
    return (
        <>
            <LayoutTabsPro
                items={[
                    {
                        label: <FormattedMessage id={'往来对账'} />,
                        key: '1',
                        action: actions(),
                        formProps: formProps('01'),
                        tableProps: tableProps(),
                    },
                    // {
                    //     label: <FormattedMessage id={'明细对账'} />,
                    //     key: '2',
                    //     action: actions(),
                    //     formProps: formProps('02'),
                    //     tableProps: tableProps(),
                    // },
                ]}
            />
            {/* 应收应付调整明细 */}
            <ModalBalanceAdjustmentDocument
                type={currentData?.type}
                dataSource={currentData}
                open={billDetailOpen}
                onCancel={() => {
                    setBillDetailOpenFalse();
                }}
                onOk={() => {
                    setBillDetailOpenFalse();
                }}
            />
        </>
    );
};

CurrentReconciliation.displayName = 'CurrentReconciliation';

export default CurrentReconciliation;

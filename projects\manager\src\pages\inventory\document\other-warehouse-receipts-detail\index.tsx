import React, { FC, useEffect, useRef, useState } from 'react';
import { Button, Space } from '@common/components';
import { Spin, Tabs } from '@weway/beacon';
import { FormattedMessage } from '@weway/i18n';
import { useKeyPress, useRequest } from 'ahooks';
import { getOtherGet } from '@common/services/api/admin/mods/inventory';
import { useLocation } from 'react-router-dom';
import { postLatestcodeGet } from '@common/services/api/admin/mods/basic';
import { useTags } from '@weway/ui';
import { AuthComponent } from '@common/components/feature';
import DocumentDetail from './components';
import OperationLog from './operation-log';

const OtherWareHouseDetail: FC = () => {
    const portalTarget = useRef<HTMLDivElement>(null);
    const ref = useRef<any>();
    const { navigate } = useTags();
    const { state } = useLocation(); //接收参数
    const onChange = (key: string) => {
        console.log(key);
    };
    const [mData, setMData] = useState<any>();
    useKeyPress('alt.s', () => {
        ref.current.submit();
    });
    useKeyPress('alt.c', () => {
        ref.current.submit();
    });

    //获取详情
    const { loading: loadingGet, runAsync: runAsyncGet } = useRequest(getOtherGet, { manual: true });

    //生成单据编号
    const { runAsync: runAsyncGetCode } = useRequest(postLatestcodeGet, { manual: true });
    const handleInit = async () => {
        try {
            const data: any = await runAsyncGet(state?.id);
            if (state?.code) {
                data.businessNumber = state?.code;
            }
            data.id = state?.id;
            setMData(data);
        } catch (error) {}
    };
    useEffect(() => {
        if (state?.id) {
            handleInit();
        }
    }, [state?.id]);
    useEffect(() => {
        if (state?.code) {
            setMData({ ...mData, businessNumber: state?.code });
        }
    }, [state?.code]);

    return (
        <Spin spinning={loadingGet}>
            <Tabs
                defaultActiveKey="1"
                items={[
                    {
                        key: '1',
                        label: <FormattedMessage id={'其他入库单详情'} />,
                        children: (
                            <DocumentDetail
                                getConfigSettingContainer={() => portalTarget}
                                ref={ref}
                                datas={mData}
                                onFinish={() => {}}
                            />
                        ),
                    },

                    {
                        key: '5',
                        label: <FormattedMessage id={'操作日志'} />,
                        children: <OperationLog onFinish={() => {}} id={state?.id} />,
                    },
                ]}
                onChange={onChange}
                tabBarExtraContent={
                    <>
                        <Space>
                            <div ref={portalTarget}></div>

                            <AuthComponent id="">
                                <Button
                                    type="primary"
                                    onClick={async () => {
                                        try {
                                            const data = await runAsyncGetCode({ type: 303 });
                                            navigate('/inventory/document/add-other-put', {
                                                state: {
                                                    code: data.value,
                                                },
                                            });
                                        } catch (error) {}
                                    }}
                                >
                                    <FormattedMessage id={'新增(N)'} />
                                </Button>
                            </AuthComponent>
                            <AuthComponent id="">
                                <Button onClick={() => {}}>
                                    <FormattedMessage id={'打印(P)'} />
                                </Button>
                            </AuthComponent>
                        </Space>
                    </>
                }
            />
        </Spin>
    );
};

OtherWareHouseDetail.displayName = 'OtherWareHouseDetail';

export default OtherWareHouseDetail;

import { Descriptions } from "@common/components/feature";
import { LayoutContent } from "@common/components/feature";
import { Button, FormatTextAuto, Modal, ModalRefAttributes } from '@common/components';
import { DescriptionsProps } from '@weway/beacon';
import { FormattedMessage } from '@weway/i18n';
import React, { FC, ReactNode, useRef, useState } from 'react';

type ModalDetailProps = {
    text: ReactNode;
    formData?: any;
};
const ModalDetail: FC<ModalDetailProps> = (props) => {
    const { text } = props;

    const modalRef = useRef<ModalRefAttributes>(null);
    const [descriptions] = useState<DescriptionsProps['items']>([
        {
            key: '1',
            label: <FormattedMessage id={'交易流水'} />,
            children: '24101710143947394340',
        },
        {
            key: '2',
            label: <FormattedMessage id={'支付单号'} />,
            children: '24101710143847762434',
        },
        {
            key: '3',
            label: <FormattedMessage id={'支付状态'} />,
            children: '已支付',
        },
        {
            key: '4',
            label: <FormattedMessage id={'结算状态'} />,
            children: '交易入账',
        },
        {
            key: '5',
            label: <FormattedMessage id={'支付金额'} />,
            children: '1.00',
        },
        {
            key: '6',
            label: <FormattedMessage id={'已退金额'} />,
            children: '1.00',
        },
        {
            key: '7',
            label: <FormattedMessage id={'发起类型'} />,
            children: '拼团',
        },
        {
            key: '8',
            label: <FormattedMessage id={'发起终端'} />,
            children: 'PC',
        },
        {
            key: '9',
            label: <FormattedMessage id={'支付类型'} />,
            children: '在线支付',
        },
        {
            key: '10',
            label: <FormattedMessage id={'支付方式'} />,
            children: '预存款支付',
        },
        {
            key: '11',
            label: <FormattedMessage id={'交易渠道'} />,
            children: '官方通道',
        },
        {
            key: '12',
            label: <FormattedMessage id={'支付应用'} />,
            children: '预存款支付',
        },
        {
            key: '13',
            label: <FormattedMessage id={'客户'} />,
            children: 'xx',
        },
        {
            key: '14',
            label: <FormattedMessage id={'单号'} />,
            children: '2410171438411636',
        },
        {
            key: '15',
            label: <FormattedMessage id={'订单'} />,
            children: '1.00',
        },
        {
            key: '16',
            label: <FormattedMessage id={'发起时间'} />,
            children: '2024/10/17 14:39:28',
        },
        {
            key: '17',
            label: <FormattedMessage id={'发起单号'} />,
            children: '2024/10/17 14:39:28',
        },
        {
            key: '18',
            label: <FormattedMessage id={'交易时间'} />,
            children: '2024/10/17 14:39:28',
        },
        {
            key: '19',
            label: <FormattedMessage id={'交易流水号'} />,
            children: '2024/10/17 14:39:28',
        },
    ]);
    return (
        <Modal
            title={<FormatTextAuto.Locales id={'支付记录'} />}
            width={840}
            text={text}
            ref={modalRef}
            footer={
                <Button
                    onClick={() => {
                        modalRef.current.hide();
                    }}
                    formatTextLocalesAutoProps={{ id: '关闭(Esc)' }}
                ></Button>
            }
            destroyOnClose
        >
            <>
                <LayoutContent>
                    <Descriptions descriptions={descriptions} column={2} />
                </LayoutContent>
            </>
        </Modal>
    );
};
export default ModalDetail;

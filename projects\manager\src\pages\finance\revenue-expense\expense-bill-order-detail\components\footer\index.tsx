import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Space, FormatTextAuto, FormattedMessageSpan, Text } from '@common/components';
import styles from './index.module.less';
import { GridInput } from '@weway/beacon';
import { FormattedMessage } from '@weway/i18n';
type FooterProps = {
    onFinish: (footData: any) => void;
    datas?: any;
};
const index = forwardRef((props: FooterProps, ref: any) => {
    const { onFinish, datas } = props;

    const [returnType1, setReturnType1] = useState(null);
    const [money1, setMoney1] = useState(null);
    const [returnType2, setReturnType2] = useState(null);
    const [money2, setMoney2] = useState(null);
    const [remark, setRemark] = useState('');
    useEffect(() => {
        setRemark(datas?.remark || '');
        setReturnType1(datas?.payments?.[0]?.method || null);
        setMoney1(datas?.payments?.[0]?.amount || null);
        setReturnType2(datas?.payments?.[1]?.method || null);
        setMoney2(datas?.payments?.[1]?.amount || null);
    }, [datas]);
    useImperativeHandle(ref, () => ({
        submit() {
            onFinish({
                remark: remark,
                payments: [
                    {
                        id: returnType1,
                        value: money1,
                    },
                    {
                        id: returnType2,
                        value: money2,
                    },
                ],
            });
        },
    }));
    return (
        <div className={styles.footerContainer}>
            <div className={styles.footerBox1}>
                <Space>
                    <FormatTextAuto.Locales id={'制单:'} />
                    <FormatTextAuto.Locales id={'张三'} />
                    <FormatTextAuto.Locales id={'2024-01-26  12:15:16'} />
                </Space>
                <Space>
                    <FormatTextAuto.Locales id={'提交:'} />
                    <FormatTextAuto.Locales id={'张三'} />
                    <FormatTextAuto.Locales id={'2024-01-26  12:15:16'} />
                </Space>
            </div>
            <div className={styles.footerBox1}>
                <Space align="start">
                    <FormatTextAuto.Locales id={'整单备注:'} />
                    <GridInput.TextArea
                        disabled
                        rows={4}
                        style={{ width: '240px' }}
                        value={remark}
                        onChange={(e) => setRemark(e.target.value)}
                    />
                </Space>
            </div>
            <div className={styles.footerBox2}>
                <Space direction="vertical">
                    <Space>
                        <FormattedMessage id="付款1" />
                        <Text>{datas?.payments?.[0]?.name}</Text>
                        <FormattedMessage id="金额" />
                        <Text>{datas?.payments?.[0]?.value}</Text>
                    </Space>
                    <Space>
                        <FormattedMessage id="付款2" />
                        <Text>{datas?.payments?.[1]?.name}</Text>
                        <FormattedMessage id="金额" />
                        <Text>{datas?.payments?.[1]?.value}</Text>
                    </Space>
                </Space>
                <div className={styles.line}></div>
                <div style={{ color: 'red' }}>
                    <Space>
                        <Space>
                            <FormattedMessageSpan id="金额" />
                            <Text>{datas?.totalAmount}</Text>
                        </Space>
                        <Space>
                            <FormattedMessageSpan id="欠款" />
                            <Text>{datas?.unPaidAmount}</Text>
                        </Space>
                    </Space>
                </div>
            </div>
        </div>
    );
});
export default index;
index.displayName = 'index';

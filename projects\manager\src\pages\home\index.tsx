import React from 'react';
import { Divider, Input, Button, message, Tooltip, Space } from '@weway/beacon';
import { FormattedMessage } from '@weway/i18n';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import appTwoDimensionalCode from '@/assets/image/home/<USER>';
import appIcon from '@/assets/image/home/<USER>';
import vxIcon from '@/assets/image/home/<USER>';
import Charts from './components/charts';
import styles from './index.module.less';
import Icon1 from '@/assets/image/home/<USER>';
import Icon2 from '@/assets/image/home/<USER>';
import Icon3 from '@/assets/image/home/<USER>';
import { DesktopOutlined } from '@ant-design/icons';
const CardLayout = (props: { children: React.ReactNode; cardLayoutClassName?: string }) => {
    const { cardLayoutClassName = '', children } = props;
    return <div className={`${styles.cardLayout} ${cardLayoutClassName ? cardLayoutClassName : ''}`}>{children}</div>;
};
export const Card = (props: { title: React.ReactNode; content: React.ReactNode; cardClassName?: string }) => {
    const { title, content, cardClassName } = props;
    return (
        <CardLayout cardLayoutClassName={cardClassName}>
            {title}
            <Divider style={{ margin: '10px 0' }} />
            {content}
        </CardLayout>
    );
};

export default function Index() {
    return (
        <div className={styles.home}>
            <div className={styles['home-left']}>
                <div className={styles['home-left-stayDo']}>
                    <Card
                        title={
                            <div className={styles['home-left-stayDo-title']}>
                                <FormattedMessage id="待办事项" />
                            </div>
                        }
                        content={
                            <div className={styles['home-left-stayDo-content']}>
                                <div className={styles['home-left-stayDo-content-item']}>
                                    <div>0</div>
                                    <div>
                                        <FormattedMessage id="待审核订单" />
                                    </div>
                                </div>
                                <div className={styles['home-left-stayDo-content-item']}>
                                    <div>0</div>
                                    <div>
                                        <FormattedMessage id="待支付订单 " />
                                    </div>
                                </div>
                                <div className={styles['home-left-stayDo-content-item']}>
                                    <div>0</div>
                                    <div>
                                        <FormattedMessage id="待发货订单 " />
                                    </div>
                                </div>
                                <div className={styles['home-left-stayDo-content-item']}>
                                    <div>0</div>
                                    <div>
                                        <FormattedMessage id="待处理退换货单 " />
                                    </div>
                                </div>
                                <div className={styles['home-left-stayDo-content-item']}>
                                    <div>0</div>
                                    <div>
                                        <FormattedMessage id="待处理退款单 " />
                                    </div>
                                </div>
                            </div>
                        }
                    />
                </div>
                <div className={styles['home-left-todayInfo']}>
                    <CardLayout
                        cardLayoutClassName={[
                            styles['home-left-todayInfo-item'],
                            styles['home-left-todayInfo-quantity'],
                        ].join(' ')}
                    >
                        <div className={styles['home-left-todayInfo-item-left']}>
                            <div className={styles['home-left-todayInfo-item-left-value']}>0</div>
                            <div className={styles['home-left-todayInfo-item-left-type']}>
                                <FormattedMessage id="今日订单数量" />
                            </div>
                        </div>
                        <div className={styles['home-left-todayInfo-item-right']}>
                            <img src={Icon1} alt="" />
                        </div>
                    </CardLayout>
                    <CardLayout
                        cardLayoutClassName={[
                            styles['home-left-todayInfo-item'],
                            styles['home-left-todayInfo-newCustomer'],
                        ].join(' ')}
                    >
                        <div className={styles['home-left-todayInfo-item-left']}>
                            <div className={styles['home-left-todayInfo-item-left-value']}>0</div>
                            <div className={styles['home-left-todayInfo-item-left-type']}>
                                <FormattedMessage id="今日新增客户" />
                            </div>
                        </div>
                        <div className={styles['home-left-todayInfo-item-right']}>
                            <img src={Icon2} alt="" />
                        </div>
                    </CardLayout>
                    <CardLayout
                        cardLayoutClassName={[
                            styles['home-left-todayInfo-item'],
                            styles['home-left-todayInfo-orderAmount'],
                        ].join(' ')}
                    >
                        <div className={styles['home-left-todayInfo-item-left']}>
                            <div className={styles['home-left-todayInfo-item-left-value']}>0</div>
                            <div className={styles['home-left-todayInfo-item-left-type']}>
                                <FormattedMessage id="今日订单金额" />
                            </div>
                        </div>
                        <div className={styles['home-left-todayInfo-item-right']}>
                            <img src={Icon3} alt="" />
                        </div>
                    </CardLayout>
                </div>
                <Card
                    title={<FormattedMessage id="经营趋势" />}
                    content={<Charts />}
                    cardClassName={styles['home-left-managementTrend']}
                />
            </div>
            <div className={styles['home-right']}>
                <Card
                    title={<FormattedMessage id="任我行科技" />}
                    content={
                        <div className={styles['home-right-systemInfo-content']}>
                            <div>
                                <Space>
                                    <FormattedMessage id="版 本：" />
                                    <FormattedMessage id="快马批发旗舰版 210414001" />
                                </Space>
                            </div>
                            <div>
                                <Space>
                                    <FormattedMessage id="有效期：" />
                                    <FormattedMessage id="2024/9/14 0:00:00 剩余360天" />
                                </Space>
                            </div>
                            <div>
                                <Space>
                                    <FormattedMessage id="用户数：" />
                                    <FormattedMessage id="无限用户" />
                                </Space>
                            </div>
                            <div>
                                <Space>
                                    <FormattedMessage id="短信数：" />
                                    <FormattedMessage id="19925条【充值】" />
                                </Space>
                            </div>
                        </div>
                    }
                    cardClassName={styles['home-right-systemInfo']}
                />
                <Card
                    title={<FormattedMessage id="后台管理地址" />}
                    content={
                        <div className={styles['home-right-apply-content']}>
                            <CopyToClipboard text={'asdsa'}>
                                <div
                                    className={[
                                        styles['home-right-apply-content-item'],
                                        styles['home-right-apply-content-computer'],
                                    ].join(' ')}
                                >
                                    <span className={styles['home-right-apply-content-item-type']}>
                                        <FormattedMessage id="电脑端" />
                                    </span>
                                    <span className={styles['home-right-apply-content-item-input']}>
                                        <Input value={'http://zy.366kmsz.com/e26r/WebSupplier'} />
                                    </span>
                                    <Button
                                        onClick={() => {
                                            message.success('复制成功 http://zy.366kmsz.com/e26r/WebSupplier');
                                        }}
                                    >
                                        <FormattedMessage id="复制" />
                                    </Button>
                                </div>
                            </CopyToClipboard>
                            <div
                                className={[
                                    styles['home-right-apply-content-item'],
                                    styles['home-right-apply-content-app'],
                                ].join(' ')}
                            >
                                <span className={styles['home-right-apply-content-item-type']}>
                                    <FormattedMessage id="手机端" />
                                </span>
                                <span className={styles['home-right-apply-content-item-input']}>
                                    <Input value={'http://zy.366kmsz.com/e26r/WebSupplier'} />
                                </span>
                                <Tooltip
                                    title={<img src={appTwoDimensionalCode} alt="" />}
                                    placement="bottom"
                                    color="#fff"
                                >
                                    <span className={styles['home-right-apply-content-item-img']}>
                                        <img src={appTwoDimensionalCode} alt="" />
                                    </span>
                                </Tooltip>
                            </div>
                            <div className={styles['home-right-apply-content-appOrVx']}>
                                <Tooltip
                                    title={
                                        <div className={styles['home-right-apply-content-appOrVx-title']}>
                                            <div>
                                                <span>Android</span>
                                                <img src={appTwoDimensionalCode} alt="" />
                                            </div>
                                            <div>
                                                <span>IOS</span>
                                                <img src={appTwoDimensionalCode} alt="" />
                                            </div>
                                        </div>
                                    }
                                    color="#fff"
                                    placement="bottom"
                                >
                                    <div className={styles['home-right-apply-content-appOrVx-item']}>
                                        <span>APP</span>
                                        <img src={appIcon} alt="" />
                                    </div>
                                </Tooltip>
                                <Tooltip
                                    title={
                                        <div className={styles['home-right-apply-content-appOrVx-title']}>
                                            <div>
                                                <span>公众号</span>
                                                <img src={appTwoDimensionalCode} alt="" />
                                            </div>
                                            <div>
                                                <span>小程序</span>
                                                <img src={appTwoDimensionalCode} alt="" />
                                            </div>
                                        </div>
                                    }
                                    color="#fff"
                                    placement="bottom"
                                >
                                    <div className={styles['home-right-apply-content-appOrVx-item']}>
                                        <span>APP</span>
                                        <img src={vxIcon} alt="" />
                                    </div>
                                </Tooltip>
                            </div>
                        </div>
                    }
                    cardClassName={styles['home-right-apply']}
                />

                <Card
                    title={<FormattedMessage id="邀请客户注册" />}
                    cardClassName={styles['home-right-invite']}
                    content={
                        <div className={styles['home-right-invite-content']}>
                            <div
                                className={[
                                    styles['home-right-invite-content-item'],
                                    styles['home-right-invite-content-methodOne'],
                                ].join(' ')}
                            >
                                <span className={styles['home-right-invite-content-item-title']}>
                                    <FormattedMessage id="方式1：" />
                                </span>
                                <span>
                                    <FormattedMessage id="发送快马数字APP二维码或微信公众号二维码，以及客户注册邀请码邀请注册。\n您的客户注册邀请码：e26r" />
                                </span>
                            </div>
                            <div className={styles['home-right-invite-content-item']}>
                                <div className={styles['home-right-invite-content-item-title']}>
                                    <FormattedMessage id="方式2：" />
                                </div>
                                <div>
                                    {' '}
                                    <FormattedMessage id="复制以下网址直接发送给您的客户" />
                                </div>
                            </div>
                            <div
                                className={[
                                    styles['home-right-invite-content-item'],
                                    styles['home-right-invite-content-copy'],
                                ].join(' ')}
                            >
                                <div className={styles['home-right-invite-content-item-title']}>
                                    <DesktopOutlined style={{ font: '' }} />
                                </div>
                                <CopyToClipboard text={'http://zy.366kmsz.com/e26r'}>
                                    <div className={styles['home-right-invite-content-copy-box']}>
                                        <Input value="http://zy.366kmsz.com/e26r" />
                                        <Button
                                            onClick={() => {
                                                message.success('复制成功 https://mzy.366kmsz.com/e26r');
                                            }}
                                        >
                                            <FormattedMessage id="复制" />
                                        </Button>
                                    </div>
                                </CopyToClipboard>
                            </div>
                            <div
                                className={[
                                    styles['home-right-invite-content-item'],
                                    styles['home-right-invite-content-copy'],
                                ].join(' ')}
                            >
                                <div className={styles['home-right-invite-content-item-title']}>icon</div>
                                <CopyToClipboard text={'https://mzy.366kmsz.com/e26r'}>
                                    <div className={styles['home-right-invite-content-copy-box']}>
                                        <Input value="https://mzy.366kmsz.com/e26r" />
                                        <Button
                                            onClick={() => {
                                                message.success('复制成功 https://mzy.366kmsz.com/e26r');
                                            }}
                                        >
                                            <FormattedMessage id="复制" />
                                        </Button>
                                    </div>
                                </CopyToClipboard>
                            </div>
                        </div>
                    }
                />
            </div>
        </div>
    );
}

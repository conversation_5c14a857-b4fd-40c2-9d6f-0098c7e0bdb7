import { FormattedMessageSpan, GridFormCollapse, GridInput, Modal, Text } from '@common/components';
import { ModalBalanceAdjustmentProps } from './data';
import { Form, GridForm, GridInputNumber, GridRadio, message } from '@weway/beacon';
import React from 'react';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useBoolean, useRequest } from 'ahooks';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
import { DEBOUNCE_TIME, GRID_FORM_MODAL_WIDTH } from '@/common';
import { postAdaptAdd } from '@common/services/api/admin/mods/finance';
import dayjs from 'dayjs';

/**
 * 余额调整
 * @returns
 */
const ModalBalanceAdjustment = (props: ModalBalanceAdjustmentProps) => {
    const { open, type = 803, dataSource, onCancel, onOk } = props;

    const [form] = Form.useForm();

    const intl = useIntl();

    const [loading, { setTrue: setLoadingTrue, setFalse: setLoadingFalse }] = useBoolean(false);

    const { runAsync: postAdaptAddRequest } = useRequest(postAdaptAdd, { manual: true, throttleWait: DEBOUNCE_TIME });

    const handleOk = async () => {
        try {
            const formData = await form.validateFields();
            const jsonData = {
                type,
                traderId: dataSource?.id,
                customerId: dataSource?.orgId,
                adjustType: formData?.adjustType,
                handlerId: formData?.handlerId,
                adjustAmount: formData.amount,
                businessDate: dayjs().toISOString(),
                brief: '',
                remark: formData.remark,
                items: [] as any[],
            };
            setLoadingTrue();
            await postAdaptAddRequest(jsonData, intl);
            form.resetFields();
            onOk?.();
            setLoadingFalse();
            message.success(intl.formatMessage({ id: '调整成功' }));
        } catch (error) {
            setLoadingFalse();
            console.log(error);
        }
    };

    const handleCancel = () => {
        onCancel?.();
    };
    const formConfig = GridFormCollapse.getLayoutConfig({
        type: 'modal-small',
        columnCount: 5,
    });
    const { width } = Modal.getLayoutConfig({ columnCount: 5, maxColumns: formConfig.column });
    return (
        <Modal
            open={open}
            title={type === 803 ? <FormattedMessageSpan id={'应收调整'} /> : <FormattedMessageSpan id={'应付调整'} />}
            width={width}
            onOk={handleOk}
            onCancel={handleCancel}
            okButtonProps={{ loading }}
            destroyOnClose
            afterClose={() => {
                //关闭弹窗时清空表单
                form.resetFields();
            }}
        >
            <GridForm {...formConfig} form={form} initialValues={{ direction: -1 }}>
                <GridForm.Item label={<FormattedMessage id={'客户'} />}>
                    <Text type="danger">{dataSource?.name}</Text>
                </GridForm.Item>
                <GridForm.Item
                    label={<FormattedMessage id={'调整类型'} />}
                    name="adjustType"
                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择调整类型' }) }]}
                    initialValue={1}
                >
                    <GridRadio.Group
                        options={[
                            { label: <FormattedMessage id={'调增'} />, value: 1 },
                            { label: <FormattedMessage id={'调减'} />, value: -1 },
                        ]}
                    />
                </GridForm.Item>
                <GridForm.Item
                    label={<FormattedMessage id={'调整金额'} />}
                    name="amount"
                    rules={[{ required: true, message: intl.formatMessage({ id: '请输入调整金额' }) }]}
                >
                    <GridInputNumber style={{ width: GRID_FORM_MODAL_WIDTH }} />
                </GridForm.Item>
                <GridForm.Item
                    label={<FormattedMessage id={'经手人'} />}
                    name="handlerId"
                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择经手人' }) }]}
                >
                    <SelectUser />
                </GridForm.Item>
                <GridForm.Item
                    label={<FormattedMessage id={'备注'} />}
                    name="remark"
                    rules={[{ required: true, message: intl.formatMessage({ id: '请输入备注' }) }]}
                >
                    <GridInput.TextArea />
                </GridForm.Item>
            </GridForm>
        </Modal>
    );
};
ModalBalanceAdjustment.displayName = 'ModalBalanceAdjustment';
export default ModalBalanceAdjustment;

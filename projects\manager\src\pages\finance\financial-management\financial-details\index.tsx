import { TABLE_BG } from '@/common';
import { BILL_TYPE, TRADER_RECORD_TYPE } from '@/common/message';
import { SelectTimeRange } from '@/components/feature/basic-infomation/select-time-range';
import { SelectTrader } from '@/components/feature/basic-infomation/select-trader';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
import { enumToSelectOptions } from '@/utils/enum-to-select-options';
import { Button, FormattedMessageSpan, GridFormCollapse, GridInput, GridSelect, Text } from '@common/components';
import { LayoutTabsPro, LayoutTabsProProps } from '@common/components/feature';
import { postAccountItemList } from '@common/services/api/admin/mods/finance';
import { Form, GridTableColumnsType, message } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import { get as _get } from 'lodash';
import React, { FC, useRef } from 'react';
import ModalTransferDetail from './components/modal-transfer-detail';
import ModalDepositDetail from './components/modal-deposit-detail';
import { useBoolean } from 'ahooks';
import ModalPaymentDetail from './components/modal-payment-detail';

const FinancialDetails: FC = () => {
    const intl = useIntl();
    const [showTransferModal, { setTrue: setShowTransferModalTrue, setFalse: setShowTransferModalFalse }] =
        useBoolean(false);
    const [showDepositModal, { setTrue: setShowDepositModalTrue, setFalse: setDepositModalFalse }] = useBoolean(false);
    const [showPaymentModal, { setTrue: setShowPaymentModalTrue, setFalse: setPaymentModalFalse }] = useBoolean(false);
    const currentId = useRef(null);
    const columns: Array<GridTableColumnsType> = [
        {
            // title: <FormattedMessage id={'日期'} />,
            dataIndex: 'businessDate',
            key: 'businessDate',
            align: 'center',
            sorter: true,
            width: 160,
            summary: false,
            renderFormatter(options) {
                return dayjs.unix(options.item.businessDate).format('YYYY-MM-DD HH:mm:ss');
            },
        },
        {
            // title: <FormattedMessage id={'收付款单号'} />,
            dataIndex: 'fromBusinessNumber',
            key: 'fromBusinessNumber',
            align: 'center',
            sorter: true,
            summary: false,
            renderFormatter(options) {
                return <FormattedMessageSpan id={options.item.fromBusinessNumber || '--'} type="link" />;
            },
        },
        {
            // title: <FormattedMessage id={'业务单号'} />,
            dataIndex: 'businessNumber',
            key: 'businessNumber',
            align: 'center',
            sorter: true,
            summary: false,
            renderFormatter(options) {
                return (
                    <FormattedMessageSpan
                        id={options.item.businessNumber || '--'}
                        type="link"
                        onClick={() => {
                            currentId.current = options.item.fromId;
                            switch (options.item.type) {
                                case 605:
                                    //转款单
                                    setShowTransferModalTrue();
                                    break;
                                case 607:
                                    //提现单
                                    setShowDepositModalTrue();
                                    break;
                                case 602:
                                    //付款单
                                    setShowPaymentModalTrue();
                                    break;
                                default:
                                    message.error('研发中....');
                            }
                        }}
                    />
                );
            },
        },
        {
            // title: <FormattedMessage id={'账户名称'} />,
            dataIndex: 'accountName',
            key: 'accountName',
            align: 'center',
            sorter: true,
        },
        {
            // title: <FormattedMessage id={'单据类型'} />,
            dataIndex: 'fromType',
            key: 'fromType',
            align: 'center',
            summary: false,
            renderFormatter(options) {
                return <FormattedMessage id={BILL_TYPE[options.item.fromType] || '--'} />;
            },
        },
        {
            // title: <FormattedMessage id={'往来单位'} />,
            dataIndex: 'traderName',
            key: 'traderName',
            align: 'center',
            sorter: true,
            summary: false,
        },
        {
            // title: <FormattedMessage id={'经手人'} />,
            dataIndex: 'handlerName',
            key: 'handlerName',
            align: 'center',
            sorter: true,
            summary: false,
        },

        {
            // title: <FormattedMessage id={'收款金额'} />,
            dataIndex: 'amount',
            key: 'amount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return options.item.direction === 1 && <Text type="danger">{options.item.amount}</Text>;
            },
        },
        {
            // title: <FormattedMessage id={'付款金额'} />,
            dataIndex: 'fromAmount',
            key: 'fromAmount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return options.item.direction === -1 && <Text type="danger">{options.item.amount}</Text>;
            },
        },

        {
            // title: <FormattedMessage id={'单据说明'} />,
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
            summary: false,
        },
    ];
    const [form] = Form.useForm();
    const formProps = (id: string) => {
        return {
            fieldMeta: [
                { name: 'timeRange', label: '时间', displayLabel: '时间' },
                { name: 'keywords', label: '账户', displayLabel: '账户' },
                { name: 'type', label: '收付类型', displayLabel: '收付类型' },
                { name: 'handlerIds', label: '经手人', displayLabel: '经手人' },
                { name: 'traderId', label: '往来单位', displayLabel: '往来单位' },

                { name: 'businessNumber', label: '单据编号', displayLabel: '单据编号' },

                { name: 'remark', label: '单据说明', displayLabel: '单据说明' },
            ],
            items: [
                {
                    name: 'timeRange',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <SelectTimeRange />
                            </GridFormCollapse.Item>
                        );
                    },
                },

                {
                    name: 'keywords',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridInput placeholder={intl.formatMessage({ id: '账户名称' })} />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'type',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridSelect options={enumToSelectOptions(TRADER_RECORD_TYPE, true)} />
                            </GridFormCollapse.Item>
                        );
                    },
                },

                {
                    name: 'handlerIds',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <SelectUser mode="multiple" />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'traderId',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <SelectTrader.Single
                                    onChange={(event: any) => {
                                        form.setFieldsValue({
                                            traderId: event.target?.dataSource?.id,
                                        });
                                    }}
                                />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'businessNumber',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridInput placeholder={intl.formatMessage({ id: '单据编号' })} />
                            </GridFormCollapse.Item>
                        );
                    },
                },

                {
                    name: 'remark',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridInput />
                            </GridFormCollapse.Item>
                        );
                    },
                },
            ],
            id: id,
            form: form,
            expandIcon: false,
            defaultCollapsed: true,
            initialValues: { type: null as any, timeRange: [dayjs().startOf('month'), dayjs().endOf('month')] },
        };
    };
    const tableProps = (): LayoutTabsProProps['items'][number]['tableProps'] => {
        return {
            columns: columns,
            rowKey: 'id',
            columnsMeta: [
                {
                    title: '日期',
                    displayTitle: '日期',
                    key: 'businessDate',
                },
                {
                    title: '收付款单号',
                    displayTitle: '收付款单号',
                    key: 'fromBusinessNumber',
                },
                {
                    title: '业务单号',
                    displayTitle: '业务单号',
                    key: 'businessNumber',
                },
                {
                    title: '单据类型',
                    displayTitle: '单据类型',
                    key: 'fromType',
                },
                {
                    title: '账户名称',
                    displayTitle: '账户名称',
                    key: 'accountName',
                },
                {
                    title: '往来单位',
                    displayTitle: '往来单位',
                    key: 'traderName',
                },
                {
                    title: '经手人',
                    displayTitle: '经手人',
                    key: 'handlerName',
                },
                {
                    title: '收款金额',
                    displayTitle: '收款金额',
                    key: 'amount',
                },
                {
                    title: '付款金额',
                    displayTitle: '付款金额',
                    key: 'fromAmount',
                },
                {
                    title: '单据说明',
                    displayTitle: '单据说明',
                    key: 'remark',
                },
            ],
            defaultSortOrder: 'descend',
            defaultSortOrderField: 'businessDate',
            sortDirections: ['ascend', 'descend', 'ascend'],
            request: async (params) => {
                try {
                    const { list, pager } = await postAccountItemList({
                        ...(pick(params, ['pager', 'sorter']) as Required<Pick<typeof params, 'pager' | 'sorter'>>),
                        ...params.form,
                        startTime: params?.form?.timeRange?.[0]
                            ? (dayjs(params?.form?.timeRange[0]).unix() as any)
                            : dayjs().startOf('month').unix(),
                        endTime: params?.form?.timeRange?.[1]
                            ? (dayjs(params?.form?.timeRange[1]).unix() as any)
                            : dayjs().endOf('month').unix(),
                    });

                    return {
                        data: list,
                        total: pager.totals,
                    };
                } catch (err) {
                    console.log(err);

                    return {
                        data: [],
                        total: 0,
                    };
                }
            },

            rowSelection: {
                type: 'checkbox',
            },
            readonly: true,
            resizable: true,
            pagination: {},
            disabledBackground: TABLE_BG,
            summaryTotal(dataSource, dataIndex) {
                return {
                    value: dataIndex
                        ? dataSource.reduce((pre, cur) => pre + (Number(_get(cur, dataIndex)) || 0), 0)
                        : '',
                };
            },
        };
    };
    const actions = () => {
        return (
            <>
                <Button>
                    <FormattedMessage id="导出Excel" />
                </Button>
            </>
        );
    };
    return (
        <>
            {/* 转款单 */}
            <ModalTransferDetail open={showTransferModal} id={currentId.current} onClose={setShowTransferModalFalse} />
            {/* 提现单 */}
            <ModalDepositDetail open={showDepositModal} id={currentId.current} onClose={setDepositModalFalse} />
            {/* 付款单 */}
            <ModalPaymentDetail open={showPaymentModal} id={currentId.current} onClose={setPaymentModalFalse} />
            <LayoutTabsPro
                items={[
                    {
                        label: <FormattedMessage id={'资金明细'} />,
                        key: '1',
                        action: actions(),
                        formProps: formProps('01'),
                        tableProps: tableProps(),
                    },
                ]}
            />
        </>
    );
};

FinancialDetails.displayName = 'FinancialDetails';

export default FinancialDetails;

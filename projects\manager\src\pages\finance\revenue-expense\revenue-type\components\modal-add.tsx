import { LayoutHeader } from '@common/components/feature';
import { FormatTextAuto, Modal, ModalRefAttributes, GridInput, ModalProps, Button } from '@common/components';
import { Form, GridCascader, GridFormProps, GridSwitch } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useKeyPress, useRequest } from 'ahooks';
import React, { FC, forwardRef, ForwardRefExoticComponent, RefAttributes, useEffect, useRef } from 'react';
import { postSubjectAdd, postSubjectEdit } from '@common/services/api/admin/mods/finance';
type ModalBaseProps = {
    onSubmitSuccess?: () => void;
    values?: any;
    dataSources?: Array<any>;
} & Pick<ModalProps, 'afterClose' | 'title' | 'okButtonProps' | 'text' | 'open' | 'onCancel' | 'footer'> &
    Pick<GridFormProps, 'onFinish' | 'onFinishFailed' | 'form' | 'disabled'>;
const ModalBase: ForwardRefExoticComponent<RefAttributes<ModalRefAttributes> & ModalBaseProps> = forwardRef(
    (props, ref) => {
        const {
            title,
            onFinish,
            onFinishFailed,
            form,
            afterClose,
            footer,
            okButtonProps,
            open,
            text,
            onCancel,
            dataSources,
            disabled = false,
        } = props;
        const intl = useIntl();
        //提交表单验证成功回调
        const _onFinish: GridFormProps['onFinish'] = (value) => {
            onFinish(value);
        };
        //提交表单验证失败回调
        const _onFinishFailed: GridFormProps['onFinishFailed'] = (value) => {
            onFinishFailed(value);
        };

        const formConfig = LayoutHeader.GridFormCollapse.getLayoutConfig({
            type: 'modal-small',
            columnCount: 7,
        });
        useKeyPress('enter', () => {
            form.submit();
        });
        const { width } = Modal.getLayoutConfig({ columnCount: 7, maxColumns: formConfig.column });

        return (
            <Modal
                title={title}
                width={width}
                onOk={form.submit}
                ref={ref}
                afterClose={afterClose}
                footer={footer}
                okButtonProps={okButtonProps}
                open={open}
                text={text}
                onCancel={onCancel}
            >
                <LayoutHeader.GridFormCollapse
                    id={'form'}
                    form={form}
                    onFinish={_onFinish}
                    onFinishFailed={_onFinishFailed}
                    {...formConfig}
                    defaultCollapsed={false}
                    renderSubmitter={() => null}
                    expandIcon={() => null}
                    disabled={disabled}
                    initialValues={{
                        status: true,
                    }}
                >
                    <LayoutHeader.GridFormCollapse.Item
                        label={<FormatTextAuto.Locales id={'科目名称'} />}
                        name="name"
                        rules={[{ required: true, message: intl.formatMessage({ id: '请输入科目名称！' }) }]}
                    >
                        <GridInput />
                    </LayoutHeader.GridFormCollapse.Item>
                    <LayoutHeader.GridFormCollapse.Item
                        label={<FormatTextAuto.Locales id={'编号'} />}
                        name="code"
                        rules={[{ required: true, message: intl.formatMessage({ id: '请输入编号！' }) }]}
                    >
                        <GridInput />
                    </LayoutHeader.GridFormCollapse.Item>
                    <LayoutHeader.GridFormCollapse.Item
                        label={<FormatTextAuto.Locales id={'父级科目'} />}
                        name="parentCode"
                        rules={[{ required: true, message: intl.formatMessage({ id: '请选择父级科目' }) }]}
                    >
                        <GridCascader
                            options={dataSources}
                            fieldNames={{ label: 'name', value: 'code', children: 'children' }}
                            changeOnSelect
                            expandTrigger="hover"
                        />
                    </LayoutHeader.GridFormCollapse.Item>
                    <LayoutHeader.GridFormCollapse.Item label={<FormatTextAuto.Locales id={'备注'} />} name="remark">
                        <GridInput />
                    </LayoutHeader.GridFormCollapse.Item>

                    <LayoutHeader.GridFormCollapse.Item
                        label={<FormatTextAuto.Locales id={'状态'} />}
                        name="isEnabled"
                        valuePropName="checked"
                        column={2}
                        labelCol={{ span: 4 }}
                        wrapperCol={{ span: 20 }}
                    >
                        <GridSwitch />
                    </LayoutHeader.GridFormCollapse.Item>
                </LayoutHeader.GridFormCollapse>
            </Modal>
        );
    },
);
ModalBase.displayName = 'ModalBase';
type ModalAddProps = {
    onSubmitSuccess?: () => void;
    dataSources: Array<any>;
    isParent: boolean;
    formValue: any;
} & Pick<ModalBaseProps, 'open' | 'onCancel'>;
export const ModalAdd: FC<ModalAddProps> = (props) => {
    const intl = useIntl();
    const { onSubmitSuccess, open, onCancel, dataSources, isParent, formValue } = props;
    const [form] = Form.useForm();
    const { runAsync, loading = false } = useRequest(postSubjectAdd, { manual: true });
    const reset = () => {
        form.resetFields();
    };
    const onFinish = async (value: any) => {
        try {
            const newData = {
                ...value,
                isEnabled: value.isEnabled ? 1 : 0,
                parentCode: Array.isArray(value.parentCode)
                    ? value.parentCode?.[value.parentCode.length - 1]
                    : value.parentCode,
            };
            await runAsync(newData, intl);
            reset();
            onSubmitSuccess();
        } catch (error) {}
    };
    useEffect(() => {
        if (open && isParent) {
            const parentIds = findParentIds(dataSources, formValue);
            form.setFieldValue('parentCode', parentIds);
        }
    }, [open]);
    const onFinishFailed = () => {};
    return (
        <ModalBase
            title={<FormattedMessage id={'新增'} />}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            form={form}
            afterClose={reset}
            okButtonProps={{ loading: loading }}
            open={open}
            onCancel={onCancel}
            dataSources={dataSources}
        />
    );
};
type ModalEditProps = {
    formValue?: any;
    onSubmitSuccess?: () => void;
    dataSources: Array<any>;
} & Pick<ModalBaseProps, 'open' | 'onCancel'>;
export const ModalEdit: FC<ModalEditProps> = (props) => {
    const [form] = Form.useForm();
    const intl = useIntl();
    const { formValue, open, onCancel, onSubmitSuccess, dataSources } = props;
    const { runAsync, loading = false } = useRequest(postSubjectEdit, { manual: true });
    useEffect(() => {
        if (open) {
            const parentIds = findParentIds(dataSources, formValue?.parentCode);
            const newData = {
                ...formValue,
                parentCode: parentIds,
            };
            form.setFieldsValue(newData);
        }
    }, [open]);
    const onFinish: ModalBaseProps['onFinish'] = async (value: any) => {
        try {
            const newData = {
                ...value,
                isEnabled: value.isEnabled ? 1 : 0,
                parentCode: Array.isArray(value.parentCode)
                    ? value.parentCode?.[value.parentCode.length - 1]
                    : value.parentCode,
            };
            await runAsync(newData, intl);
            onSubmitSuccess();
        } catch (error) {}
    };
    const onFinishFailed: ModalBaseProps['onFinishFailed'] = () => {};
    return (
        <ModalBase
            form={form}
            title={<FormatTextAuto.Locales id={'修改'} />}
            open={open}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            okButtonProps={{ loading: loading }}
            onCancel={onCancel}
            dataSources={dataSources}
        />
    );
};

type ModalLookProps = {
    dataSources: Array<any>;
    formValue?: any;
    onClose: () => void;
} & Pick<ModalBaseProps, 'open' | 'onCancel'>;
export const ModalLook: FC<ModalLookProps> = (props) => {
    const [form] = Form.useForm();
    const { open, onClose, dataSources, formValue } = props;
    const modalRef = useRef<ModalRefAttributes>(null);
    const onFinish: ModalBaseProps['onFinish'] = () => {};
    const onFinishFailed: ModalBaseProps['onFinishFailed'] = () => {};
    useEffect(() => {
        if (open) {
            const parentIds = findParentIds(dataSources, formValue?.parentCode);
            const newData = {
                ...formValue,
                parentCode: parentIds,
            };
            form.setFieldsValue(newData);
        }
    }, [open]);
    return (
        <ModalBase
            ref={modalRef}
            form={form}
            title={<FormattedMessage id={'查看'} />}
            open={open}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            footer={
                <Button
                    onClick={() => {
                        onClose();
                    }}
                >
                    <FormattedMessage id="关闭(Esc)" />
                </Button>
            }
            onCancel={onClose}
            dataSources={dataSources}
            disabled={true}
        />
    );
};
/**
 * 递归查找目标id的父级列表，GridCascader需要使用id数组作为参数
 * @param list 数据列表
 * @param targetId 查找的id
 * @returns number[] 对应父级id的数组
 */
const findParentIds = (list: any[], targetId: number): number[] => {
    for (const item of list) {
        if (item.code === targetId) {
            // 如果找到目标id，返回一个包含该id的数组（即从目标id开始）
            return [item.code];
        }

        // 如果当前项有子节点，递归查找子节点
        if (item.children) {
            const result = findParentIds(item.children, targetId);
            if (result.length > 0) {
                // 如果在子节点中找到了目标id，返回父级id（当前id + 子节点返回的父级id）
                return [item.code, ...result];
            }
        }
    }

    // 如果没有找到目标id，返回空数组
    return [];
};

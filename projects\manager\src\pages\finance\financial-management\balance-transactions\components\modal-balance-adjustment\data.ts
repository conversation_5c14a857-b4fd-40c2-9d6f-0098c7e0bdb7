import { ModalProps } from '@common/components';
import defs from '@common/services/api/admin/api';

export type ModalBalanceAdjustmentProps = {
    dataSource: defs.admin.Km_Business_Services_Basic_Response_TraderBalanceListResponse__Item;
    /**
     * 803  应收调整
     * 804  应付调整
     * 606  资金调整
     */
    type: 803 | 804 | 606;
    onOk: () => void;
    onCancel: () => void;
} & Pick<ModalProps, 'text' | 'open'>;

import React, { forwardRef, useState } from 'react';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { GridInput, message, Spin } from '@weway/beacon';
import { Button, Space } from '@common/components';
import { useRequest } from 'ahooks';
import { postTransferLogAdd, postTransferLogList } from '@common/services/api/admin/mods/inventory';
import dayjs from 'dayjs';
import { LOG_OPERATION_TYPE } from '@/common/message';
import { ConfigGridTable, ConfigGridTableColumnsType } from '@common/components/feature';
import { GRID_FORM_LAYOUT_WIDTH } from '@/common';
/**
 * 单据详情
 * @returns
 */
const OperationLog = forwardRef((props: any, _ref: any) => {
    const { id } = props;
    const intl = useIntl();
    const columns: Array<ConfigGridTableColumnsType<any>> = [
        {
            title: '操作时间',
            dataIndex: 'creationTime',
            key: 'creationTime',
            align: 'center',
            renderFormatter(options) {
                return dayjs.unix(options.item.creationTime).format('YYYY-MM-DD HH:mm:ss');
            },
        },
        {
            title: '操作人',
            dataIndex: 'createdName',
            key: 'createdName',
            align: 'center',
        },
        {
            title: '操作ip',
            dataIndex: 'ipAddress',
            key: 'ipAddress',
        },
        {
            title: '操作类型',
            dataIndex: 'operationType',
            key: 'operationType',
            renderFormatter(options) {
                return <FormattedMessage id={LOG_OPERATION_TYPE[options.item.operationType]} />;
            },
        },
        {
            title: '操作日志',
            dataIndex: 'content',
            key: 'content',
        },
    ];
    const { data, loading, refreshAsync } = useRequest(() => {
        return postTransferLogList({ id: id });
    });
    const { runAsync, loading: loadingAdd } = useRequest(postTransferLogAdd, {
        manual: true,
    });
    const [content, setContent] = useState('');
    return (
        <Spin spinning={loading || loadingAdd}>
            <Space direction="vertical">
                <ConfigGridTable
                    configId=""
                    resizable
                    columns={columns}
                    rowKey={'id'}
                    readonly={true}
                    dataSource={data?.logs || []}
                    editableIcon={false}
                    align="center"
                    disabledBackground="#fff"
                />
                <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'end' }}>
                    <FormattedMessage id="追加日志:" />
                    <GridInput.TextArea
                        style={{ width: GRID_FORM_LAYOUT_WIDTH, marginLeft: 10, marginRight: 10 }}
                        onChange={(e) => {
                            setContent(e.target.value);
                        }}
                        value={content}
                    />
                    <Button
                        type="primary"
                        onClick={async () => {
                            try {
                                await runAsync({ transferOrderId: id, content: content }, intl);
                                setContent('');
                                message.success(intl.formatMessage({ id: '日志追加成功' }));
                                refreshAsync();
                            } catch (error) {}
                        }}
                    >
                        <FormattedMessage id="确认追加" />
                    </Button>
                </div>
            </Space>
        </Spin>
    );
});

export default OperationLog;
OperationLog.displayName = 'OperationLog';

import React, { FC, useRef, useState } from 'react';
import { Button, FormattedMessageSpan, GridFormCollapse, GridSelect, Space, Text } from '@common/components';
import { ProMaxTable, SelectTimeRange } from '@common/components/feature';
import {
    GridInput,
    GridTableColumnsType,
    GridTableComponentRefAttributesType,
    InputNumber,
    message,
    Tag,
} from '@weway/beacon';
import { useBoolean } from 'ahooks';
import { pick } from 'lodash';
import { timestampToDate } from '@/utils';
import dayjs from 'dayjs';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { postCashoutList } from '@common/services/api/admin/mods/customer';
import {
    ModalWithdrawBillAudit,
    ModalWithdrawBillDetail,
    ModalWithdrawBillWithdrawal,
} from './components/withdrawal-bill-detail';
import { ModalBatchAudit, ModalBatchWithdraw } from './components/batch-modal';

export const WAY_KEY: Record<number, string> = {
    0: '其他',
    1: '银行卡',
    2: '微信',
    3: '支付宝',
};
export const TYPE_KEY: Record<number, string> = {
    0: '预存款提现',
    1: '分销佣金提现',
};
interface StatusTag {
    color: string;
    style: React.CSSProperties;
    id: string;
}
const STATUS_TAGSS: Record<number, StatusTag> = {
    '8': {
        color: '#C8F3CC',
        style: {
            color: '#ffffff',
            background: '#797979',
        },
        id: '已拒绝',
    },
    '9': {
        color: '#C8F3CC',
        style: {
            color: '#ffffff',
            background: '#79000F',
        },
        id: '待审核',
    },
    '70': {
        color: '#C8F3CC',
        style: {
            color: '#ffffff',
            background: '#FD3434',
        },
        id: '待提现',
    },
    '100': {
        color: '#C8F3CC',
        style: {
            color: '#ffffff',
            background: '#11a265',
        },
        id: '已提现',
    },
};
const CustomerWithdrawalManagement: FC = () => {
    const tableRef = useRef<GridTableComponentRefAttributesType>(null);
    const tableExhibitRef = useRef<GridTableComponentRefAttributesType>(null);
    const proMaxTableRef = useRef<{ setActiveKey: (key: string) => void }>(null);
    const intl = useIntl();
    const confirmId = useRef(undefined);
    const confirmDetailId = useRef(undefined);
    // const summaryId = useRef(undefined);
    const [form1] = GridFormCollapse.useForm();
    const [form2] = GridFormCollapse.useForm();
    const [form3] = GridFormCollapse.useForm();
    const [form4] = GridFormCollapse.useForm();
    const [form5] = GridFormCollapse.useForm();
    const [autoHeight] = useBoolean(true);
    const [detailOpen, { setTrue: setDetailOpenTrue, setFalse: setDetailOpenFalse }] = useBoolean(false);
    const [auditOpen, { setTrue: setAuditOpenTrue, setFalse: setAuditOpenFalse }] = useBoolean(false);
    const [withdrawalOpen, { setTrue: setWithdrawalOpenTrue, setFalse: setWithdrawalOpenFalse }] = useBoolean(false);
    const [batchAuditOpen, { setTrue: setBatchAuditOpenTrue, setFalse: setBatchAuditOpenFalse }] = useBoolean(false);
    const [batchWithdrawalOpen, { setTrue: setBatchWithdrawalOpenTrue, setFalse: setBatchWithdrawalOpenFalse }] =
        useBoolean(false);

    const [auditTitle, setAuditTitle] = useState(0);
    const [withdrawalTitle, setWithdrawalTitle] = useState(0);

    const columns: Array<GridTableColumnsType> = [
        {
            title: '操作',
            dataIndex: 'options',
            key: 'options',
            align: 'center',
            alwaysControl: true,
            ellipsis: true,
            editableIcon: false,
            renderControl: (options) => (
                <Space>
                    {options.item.status === 9 && (
                        <FormattedMessageSpan
                            type="link"
                            id="审核"
                            onClick={() => {
                                confirmDetailId.current = options.item;
                                setAuditOpenTrue();
                            }}
                        />
                    )}
                    {options.item.status === 70 && (
                        <FormattedMessageSpan
                            type="link"
                            id="提现"
                            onClick={() => {
                                confirmDetailId.current = options.item;
                                setWithdrawalOpenTrue();
                            }}
                        />
                    )}
                </Space>
            ),
        },
        {
            title: '申请时间',
            dataIndex: 'appliedTime',
            key: 'appliedTime',
            align: 'center',
            sorter: true,
            width: 160,
            renderFormatter(options) {
                return <Text>{timestampToDate(Number(options.value))}</Text>;
            },
        },
        {
            title: '提取单号',
            dataIndex: 'businessNumber',
            key: 'businessNumber',
            align: 'center',
            sorter: true,
            width: 160,
            renderFormatter(options) {
                return (
                    <Text
                        type="link"
                        onClick={() => {
                            confirmDetailId.current = options.item;
                            const status = options.item.status;
                            if (status === 9) {
                                setAuditOpenTrue();
                            } else if (status === 70) {
                                setWithdrawalOpenTrue();
                            } else {
                                setDetailOpenTrue();
                            }
                        }}
                    >
                        {options.value}
                    </Text>
                );
            },
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            align: 'center',
            renderFormatter(options) {
                const { style, id } = STATUS_TAGSS[Number(options.value)];
                return (
                    <Tag style={style}>
                        <FormattedMessage id={id} />
                    </Tag>
                );
            },
        },
        {
            title: '客户名称',
            dataIndex: 'customerName',
            key: 'customerName',
            align: 'center',
            sorter: true,
        },
        {
            title: '客户编号',
            dataIndex: 'customerSn',
            key: 'customerSn',
            align: 'center',
            sorter: true,
        },

        {
            title: '登录账号',
            dataIndex: 'customerUserName',
            key: 'customerUserName',
            align: 'center',
        },
        {
            title: '提现类型',
            dataIndex: 'type',
            key: 'type',
            align: 'center',
            renderFormatter(options) {
                return <FormattedMessage id={TYPE_KEY[options.value as number] ?? '无'} />;
            },
        },
        {
            title: '提现金额',
            dataIndex: 'amount',
            key: 'amount',
            align: 'center',
            // renderFormatter(options: any) {
            //     const tags = options.value?.map((tag: any) => tag.name);
            //     return tags.length ? <Text>{tags.join(', ')}</Text> : '';
            // },
        },
        {
            title: '提现方式',
            dataIndex: 'way',
            key: 'way',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return <FormattedMessage id={WAY_KEY[options.value as number] ?? '无'} />;
            },
        },
        {
            title: '审核人',
            dataIndex: 'auditedUserName',
            key: 'auditedUserName',
            width: 180,
            align: 'center',
        },
        {
            title: '提现时间',
            dataIndex: 'confirmedTime',
            key: 'confirmedTime',
            align: 'center',
            sorter: true,
            width: 160,
            renderFormatter(options) {
                return <Text>{timestampToDate(Number(options.value))}</Text>;
            },
        },
        {
            title: '付款账户',
            dataIndex: 'payAccount',
            key: 'payAccount',
            align: 'center',
        },
        {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
            width: 180,
        },
    ];

    return (
        <div>
            {/*提现单--详情 */}
            <ModalWithdrawBillDetail
                open={detailOpen}
                onCancel={setDetailOpenFalse}
                item={confirmDetailId.current}
                // onSubmitSuccess={() => {
                //     message.success(intl.formatMessage({ id: '添加成功' }));
                //     tableRef.current.reload();
                //     setDetailOpenFalse();
                // }}
            />
            {/*提现单--审核 */}
            <ModalWithdrawBillAudit
                open={auditOpen}
                onCancel={setAuditOpenFalse}
                item={confirmDetailId.current}
                onSubmitSuccess={() => {
                    message.success(intl.formatMessage({ id: '审核成功' }));
                    tableRef.current.reload();
                    setAuditOpenFalse();
                }}
            />
            {/*提现单--提现 */}
            <ModalWithdrawBillWithdrawal
                open={withdrawalOpen}
                onCancel={setWithdrawalOpenFalse}
                item={confirmDetailId.current}
                onSubmitSuccess={() => {
                    message.success(intl.formatMessage({ id: '申请成功' }));
                    tableRef.current.reload();
                    setWithdrawalOpenFalse();
                }}
            />
            {/* 批量-审核 */}
            <ModalBatchAudit
                open={batchAuditOpen}
                onCancel={setBatchAuditOpenFalse}
                item={confirmId.current}
                onSubmitSuccess={() => {
                    message.success(intl.formatMessage({ id: '审核成功' }));
                    tableRef.current.reload();
                    setBatchAuditOpenFalse();
                }}
            />
            {/* 批量-提现 */}
            <ModalBatchWithdraw
                open={batchWithdrawalOpen}
                onCancel={setBatchWithdrawalOpenFalse}
                item={confirmId.current}
                onSubmitSuccess={() => {
                    message.success(intl.formatMessage({ id: '申请成功' }));
                    tableRef.current.reload();
                    setBatchWithdrawalOpenFalse();
                }}
            />
            <ProMaxTable
                defaultActiveKey="1"
                ref={proMaxTableRef as any}
                // pageSettingApi={pageSettingApi}
                autoHeight={autoHeight}
                items={[
                    {
                        key: '1',
                        label: '待审核',
                        sum: auditTitle,
                        action: (
                            <>
                                <Button
                                    onClick={() => {
                                        const data = tableRef.current.getSelectItems();
                                        if (data?.length) {
                                            confirmId.current = data.map((item) => item.id);
                                            setBatchAuditOpenTrue();
                                        } else {
                                            message.error(intl.formatMessage({ id: '请选择' }));
                                        }
                                    }}
                                >
                                    <FormattedMessage id="批量审核" />
                                </Button>
                                <Button>
                                    <FormattedMessage id="导出Excel" />
                                </Button>
                            </>
                        ),
                        header: {
                            id: -1,

                            first: <></>,
                            formProps: {
                                form: form1,
                                expandIcon: false,
                                items: [
                                    {
                                        title: '时间',
                                        field: 'timeRange1',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <SelectTimeRange />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '客户',
                                        field: 'customer',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridInput placeholder={intl.formatMessage({ id: '客户名称/编号' })} />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现类型',
                                        field: 'type',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    options={[
                                                        { label: '预存款提现', value: 0 },
                                                        { label: '分销佣金提现', value: 1 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现方式',
                                        field: 'way',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    options={[
                                                        { label: '其他', value: 0 },
                                                        { label: '银行卡', value: 1 },
                                                        { label: '微信', value: 2 },
                                                        { label: '支付宝', value: 3 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现金额',
                                        field: 'amount',
                                        render(options: any) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.name}
                                                >
                                                    <Space>
                                                        <GridFormCollapse.Item name="amountMin">
                                                            <InputNumber
                                                                min={0}
                                                                style={{ width: '100%' }}
                                                                placeholder={intl.formatMessage({ id: '最低' })}
                                                            />
                                                        </GridFormCollapse.Item>
                                                        <span>至</span>
                                                        <GridFormCollapse.Item name="amountMax">
                                                            <InputNumber
                                                                min={0}
                                                                style={{ width: '100%' }}
                                                                placeholder={intl.formatMessage({ id: '最高' })}
                                                            />
                                                        </GridFormCollapse.Item>
                                                    </Space>
                                                </GridFormCollapse.Item>
                                            );
                                        },
                                    },
                                ],
                                id: '01',
                                initialValues: {
                                    // name: 'nihao1',
                                },
                            },
                            // last: <>last</>,
                        },
                        content: {
                            id: -1,
                            tableProps: {
                                rowKey: 'id',
                                columns: columns,
                                resizable: true,
                                readonly: true,
                                ref: tableRef,
                                // loading: loading,
                                pagination: {},
                                rowSelection: {
                                    type: 'checkbox',
                                },
                                defaultSortOrder: 'ascend',
                                defaultSortOrderField: 'appliedTime',
                                sortDirections: ['ascend', 'descend', 'ascend'],
                                request: async (params) => {
                                    const { timeRange1, ...otherParams } = params.form;
                                    try {
                                        const { list, pager } = await postCashoutList(
                                            {
                                                ...(pick(params, ['pager', 'sorter']) as Required<
                                                    Pick<typeof params, 'pager' | 'sorter'>
                                                >),
                                                applyStartTime: timeRange1?.[0]
                                                    ? (dayjs(timeRange1[0]).unix() as any)
                                                    : dayjs().add(-6, 'd').unix(),
                                                applyEndTime: timeRange1?.[1]
                                                    ? (dayjs(timeRange1[1]).unix() as any)
                                                    : dayjs().unix(),
                                                status: 9,
                                                ...otherParams,
                                            },
                                            intl,
                                        );
                                        setAuditTitle(pager.totals || 0);
                                        return {
                                            data: list,
                                            total: pager.totals,
                                        };
                                    } catch (err) {
                                        return {
                                            data: [],
                                            total: 0,
                                        };
                                    }
                                },
                            },
                            foldedNavProps: null,
                        },
                        // footer: { children: <div style={{ height: 50, background: 'red' }}>footer</div> },
                    },
                    {
                        key: '2',
                        label: '待提现',
                        sum: withdrawalTitle,
                        action: (
                            <>
                                <Button
                                    onClick={() => {
                                        const data = tableRef.current.getSelectItems();
                                        if (data?.length) {
                                            confirmId.current = data.map((item) => item.id);
                                            setBatchWithdrawalOpenTrue();
                                        } else {
                                            message.error(intl.formatMessage({ id: '请选择' }));
                                        }
                                    }}
                                >
                                    <FormattedMessage id="批量提现" />
                                </Button>
                                <Button>
                                    <FormattedMessage id="导出Excel" />
                                </Button>
                            </>
                        ),
                        header: {
                            id: -1,
                            first: <></>,

                            formProps: {
                                form: form2,
                                expandIcon: false,
                                items: [
                                    {
                                        title: '时间',
                                        field: 'timeRange2',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <SelectTimeRange />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '客户',
                                        field: 'customer',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridInput placeholder={intl.formatMessage({ id: '客户名称/编号' })} />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现类型',
                                        field: 'type',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    options={[
                                                        { label: '预存款提现', value: 0 },
                                                        { label: '分销佣金提现', value: 1 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现方式',
                                        field: 'way',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    options={[
                                                        { label: '其他', value: 0 },
                                                        { label: '银行卡', value: 1 },
                                                        { label: '微信', value: 2 },
                                                        { label: '支付宝', value: 3 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现金额',
                                        field: 'amount',
                                        render(options: any) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.name}
                                                >
                                                    <Space>
                                                        <GridFormCollapse.Item name="amountMin">
                                                            <InputNumber
                                                                min={0}
                                                                style={{ width: '100%' }}
                                                                placeholder={intl.formatMessage({ id: '最低' })}
                                                            />
                                                        </GridFormCollapse.Item>
                                                        <span>至</span>
                                                        <GridFormCollapse.Item name="amountMax">
                                                            <InputNumber
                                                                min={0}
                                                                style={{ width: '100%' }}
                                                                placeholder={intl.formatMessage({ id: '最高' })}
                                                            />
                                                        </GridFormCollapse.Item>
                                                    </Space>
                                                </GridFormCollapse.Item>
                                            );
                                        },
                                    },
                                ],
                                id: '02',
                                initialValues: {
                                    // name: 'nihao1',
                                },
                            },
                        },
                        content: {
                            // id: contentId,
                            tableProps: {
                                rowKey: 'id',
                                columns: columns,
                                readonly: true,
                                resizable: true,
                                pagination: {},
                                rowSelection: {
                                    type: 'checkbox',
                                },
                                defaultSortOrder: 'ascend',
                                defaultSortOrderField: 'appliedTime',
                                sortDirections: ['ascend', 'descend', 'ascend'],
                                request: async (params) => {
                                    const { timeRange2, ...otherParams } = params.form;

                                    try {
                                        const { list, pager } = await postCashoutList(
                                            {
                                                ...(pick(params, ['pager', 'sorter']) as Required<
                                                    Pick<typeof params, 'pager' | 'sorter'>
                                                >),
                                                applyStartTime: timeRange2?.[0]
                                                    ? (dayjs(timeRange2[0]).unix() as any)
                                                    : dayjs().add(-37, 'd').unix(),
                                                applyEndTime: timeRange2?.[1]
                                                    ? (dayjs(timeRange2[1]).unix() as any)
                                                    : dayjs().unix(),
                                                status: 70,
                                                ...otherParams,
                                            },
                                            intl,
                                        );
                                        setWithdrawalTitle(pager.totals || 0);
                                        return {
                                            data: list,
                                            total: pager.totals,
                                        };
                                    } catch (err) {
                                        return {
                                            data: [],
                                            total: 0,
                                        };
                                    }
                                },
                            },
                        },
                    },
                    {
                        key: '3',
                        label: '已提现',
                        action: (
                            <>
                                <Button>
                                    <FormattedMessage id="导出Excel" />
                                </Button>
                            </>
                        ),
                        header: {
                            id: -1,
                            first: <></>,
                            formProps: {
                                form: form3,
                                expandIcon: false,
                                items: [
                                    {
                                        title: '时间',
                                        field: 'timeRange3',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <SelectTimeRange />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '客户',
                                        field: 'customer',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridInput placeholder={intl.formatMessage({ id: '客户名称/编号' })} />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现类型',
                                        field: 'type',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    options={[
                                                        { label: '预存款提现', value: 0 },
                                                        { label: '分销佣金提现', value: 1 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现方式',
                                        field: 'way',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    options={[
                                                        { label: '其他', value: 0 },
                                                        { label: '银行卡', value: 1 },
                                                        { label: '微信', value: 2 },
                                                        { label: '支付宝', value: 3 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现金额',
                                        field: 'amount',
                                        render(options: any) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.name}
                                                >
                                                    <Space>
                                                        <GridFormCollapse.Item name="amountMin">
                                                            <InputNumber
                                                                min={0}
                                                                style={{ width: '100%' }}
                                                                placeholder={intl.formatMessage({ id: '最低' })}
                                                            />
                                                        </GridFormCollapse.Item>
                                                        <span>至</span>
                                                        <GridFormCollapse.Item name="amountMax">
                                                            <InputNumber
                                                                min={0}
                                                                style={{ width: '100%' }}
                                                                placeholder={intl.formatMessage({ id: '最高' })}
                                                            />
                                                        </GridFormCollapse.Item>
                                                    </Space>
                                                </GridFormCollapse.Item>
                                            );
                                        },
                                    },
                                ],
                                id: '03',
                                initialValues: {
                                    // name: 'nihao1',
                                },
                            },
                        },
                        content: {
                            id: -1,
                            tableProps: {
                                rowKey: 'id',
                                ref: tableExhibitRef,
                                columns: columns,
                                // loading: loadingExhibit,
                                readonly: true,
                                resizable: true,
                                pagination: {},
                                rowSelection: {
                                    type: 'checkbox',
                                },
                                defaultSortOrder: 'ascend',
                                defaultSortOrderField: 'appliedTime',
                                sortDirections: ['ascend', 'descend', 'ascend'],
                                request: async (params) => {
                                    const { timeRange3, ...otherParams } = params.form;
                                    try {
                                        const { list, pager } = await postCashoutList(
                                            {
                                                ...(pick(params, ['pager', 'sorter']) as Required<
                                                    Pick<typeof params, 'pager' | 'sorter'>
                                                >),
                                                applyStartTime: timeRange3?.[0]
                                                    ? (dayjs(timeRange3[0]).unix() as any)
                                                    : dayjs().add(-37, 'd').unix(),
                                                applyEndTime: timeRange3?.[1]
                                                    ? (dayjs(timeRange3[1]).unix() as any)
                                                    : dayjs().unix(),
                                                status: 100,
                                                ...otherParams,
                                            },
                                            intl,
                                        );
                                        return {
                                            data: list,
                                            total: pager.totals,
                                        };
                                    } catch (err) {
                                        return {
                                            data: [],
                                            total: 0,
                                        };
                                    }
                                },
                            },
                        },
                    },
                    {
                        key: '4',
                        label: '已拒绝',
                        action: (
                            <>
                                <Button>
                                    <FormattedMessage id="导出Excel" />
                                </Button>
                            </>
                        ),
                        header: {
                            id: -1,
                            first: <></>,
                            formProps: {
                                form: form4,
                                expandIcon: false,
                                items: [
                                    {
                                        title: '时间',
                                        field: 'timeRange4',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <SelectTimeRange />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '客户',
                                        field: 'customer',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridInput placeholder={intl.formatMessage({ id: '客户名称/编号' })} />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现类型',
                                        field: 'type',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    options={[
                                                        { label: '预存款提现', value: 0 },
                                                        { label: '分销佣金提现', value: 1 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现方式',
                                        field: 'way',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    options={[
                                                        { label: '其他', value: 0 },
                                                        { label: '银行卡', value: 1 },
                                                        { label: '微信', value: 2 },
                                                        { label: '支付宝', value: 3 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现金额',
                                        field: 'amount',
                                        render(options: any) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.name}
                                                >
                                                    <Space>
                                                        <GridFormCollapse.Item name="amountMin">
                                                            <InputNumber
                                                                min={0}
                                                                style={{ width: '100%' }}
                                                                placeholder={intl.formatMessage({ id: '最低' })}
                                                            />
                                                        </GridFormCollapse.Item>
                                                        <span>至</span>
                                                        <GridFormCollapse.Item name="amountMax">
                                                            <InputNumber
                                                                min={0}
                                                                style={{ width: '100%' }}
                                                                placeholder={intl.formatMessage({ id: '最高' })}
                                                            />
                                                        </GridFormCollapse.Item>
                                                    </Space>
                                                </GridFormCollapse.Item>
                                            );
                                        },
                                    },
                                ],
                                id: '04',
                                initialValues: {
                                    // name: 'nihao1',
                                },
                            },
                            // last: <>last</>,
                        },
                        content: {
                            id: -1,
                            tableProps: {
                                rowKey: 'id',
                                columns: columns,
                                readonly: true,
                                ref: tableRef,
                                // loading: loading,
                                pagination: {},
                                rowSelection: {
                                    type: 'checkbox',
                                },
                                defaultSortOrder: 'ascend',
                                defaultSortOrderField: 'appliedTime',
                                sortDirections: ['ascend', 'descend', 'ascend'],
                                request: async (params) => {
                                    const { timeRange4, ...otherParams } = params.form;
                                    try {
                                        const { list, pager } = await postCashoutList(
                                            {
                                                ...(pick(params, ['pager', 'sorter']) as Required<
                                                    Pick<typeof params, 'pager' | 'sorter'>
                                                >),
                                                applyStartTime: timeRange4?.[0]
                                                    ? (dayjs(timeRange4[0]).unix() as any)
                                                    : dayjs().add(-6, 'd').unix(),
                                                applyEndTime: timeRange4?.[1]
                                                    ? (dayjs(timeRange4[1]).unix() as any)
                                                    : dayjs().unix(),
                                                status: 8,
                                                ...otherParams,
                                            },
                                            intl,
                                        );
                                        return {
                                            data: list,
                                            total: pager.totals,
                                        };
                                    } catch (err) {
                                        return {
                                            data: [],
                                            total: 0,
                                        };
                                    }
                                },
                            },
                            foldedNavProps: null,
                        },
                        // footer: { children: <div style={{ height: 50, background: 'red' }}>footer</div> },
                    },
                    {
                        key: '5',
                        label: '全部',
                        action: (
                            <>
                                <Button>
                                    <FormattedMessage id="导出Excel" />
                                </Button>
                            </>
                        ),
                        header: {
                            id: -1,
                            first: <></>,
                            formProps: {
                                form: form5,
                                expandIcon: false,
                                items: [
                                    {
                                        title: '时间',
                                        field: 'timeRange5',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <SelectTimeRange />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '客户',
                                        field: 'customer',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridInput placeholder={intl.formatMessage({ id: '客户名称/编号' })} />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现类型',
                                        field: 'type',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    options={[
                                                        { label: '预存款提现', value: 0 },
                                                        { label: '分销佣金提现', value: 1 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现方式',
                                        field: 'way',
                                        render: (options) => (
                                            <GridFormCollapse.Item
                                                label={options.name}
                                                name={options.field}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    options={[
                                                        { label: '其他', value: 0 },
                                                        { label: '银行卡', value: 1 },
                                                        { label: '微信', value: 2 },
                                                        { label: '支付宝', value: 3 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        ),
                                    },
                                    {
                                        title: '提现金额',
                                        field: 'amount',
                                        render(options: any) {
                                            return (
                                                <GridFormCollapse.Item
                                                    label={options.name}
                                                    name={options.field}
                                                    key={options.name}
                                                >
                                                    <Space>
                                                        <GridFormCollapse.Item name="amountMin">
                                                            <InputNumber
                                                                min={0}
                                                                style={{ width: '100%' }}
                                                                placeholder={intl.formatMessage({ id: '最低' })}
                                                            />
                                                        </GridFormCollapse.Item>
                                                        <span>至</span>
                                                        <GridFormCollapse.Item name="amountMax">
                                                            <InputNumber
                                                                min={0}
                                                                style={{ width: '100%' }}
                                                                placeholder={intl.formatMessage({ id: '最高' })}
                                                            />
                                                        </GridFormCollapse.Item>
                                                    </Space>
                                                </GridFormCollapse.Item>
                                            );
                                        },
                                    },
                                ],
                                id: '05',
                                initialValues: {
                                    // name: 'nihao1',
                                },
                            },
                            // last: <>last</>,
                        },
                        content: {
                            id: -1,
                            tableProps: {
                                rowKey: 'id',
                                columns: columns,
                                readonly: true,
                                ref: tableRef,
                                // loading: loading,
                                pagination: {},
                                rowSelection: {
                                    type: 'checkbox',
                                },
                                defaultSortOrder: 'ascend',
                                defaultSortOrderField: 'appliedTime',
                                sortDirections: ['ascend', 'descend', 'ascend'],
                                request: async (params) => {
                                    const { timeRange5, ...otherParams } = params.form;
                                    try {
                                        const { list, pager } = await postCashoutList(
                                            {
                                                ...(pick(params, ['pager', 'sorter']) as Required<
                                                    Pick<typeof params, 'pager' | 'sorter'>
                                                >),
                                                applyStartTime: timeRange5?.[0]
                                                    ? (dayjs(timeRange5[0]).unix() as any)
                                                    : dayjs().add(-6, 'd').unix(),
                                                applyEndTime: timeRange5?.[1]
                                                    ? (dayjs(timeRange5[1]).unix() as any)
                                                    : dayjs().unix(),
                                                status: 0,
                                                ...otherParams,
                                            },
                                            intl,
                                        );
                                        return {
                                            data: list,
                                            total: pager.totals,
                                        };
                                    } catch (err) {
                                        return {
                                            data: [],
                                            total: 0,
                                        };
                                    }
                                },
                            },
                            foldedNavProps: null,
                        },
                        // footer: { children: <div style={{ height: 50, background: 'red' }}>footer</div> },
                    },
                ]}
            ></ProMaxTable>
        </div>
    );
};

CustomerWithdrawalManagement.displayName = 'CustomerWithdrawalManagement';

export default CustomerWithdrawalManagement;

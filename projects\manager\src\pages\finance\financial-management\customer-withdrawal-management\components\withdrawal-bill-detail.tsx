import { TABLE_BG } from '@/common';
import { LOG_OPERATION_TYPE } from '@/common/message';
import { useBankAccount } from '@/hooks/useBankAccount';
import { timestampToDate } from '@/utils';
import {
    Button,
    FormatTextAuto,
    GridFormCollapse,
    GridSelect,
    Modal,
    ModalProps,
    ModalRefAttributes,
    Space,
} from '@common/components';
import { ProSettingTable } from '@common/components/feature';
import { getCashoutGet, postCashoutAudit, postCashoutEnsure } from '@common/services/api/admin/mods/customer';
import {
    Descriptions,
    Form,
    GRID_TABLE_ACTIONS_DELETE,
    GridFormProps,
    GridInput,
    GridTableColumnsType,
    QRCode,
    Tag,
} from '@weway/beacon';
import { UseGridTableRefAttributesReturn } from '@weway/beacon/dist/grid-table/hooks/data';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useBoolean, useRequest } from 'ahooks';
import React, { FC, forwardRef, ForwardRefExoticComponent, RefAttributes, useEffect, useRef, useState } from 'react';
interface StatusTag {
    color: string;
    style: React.CSSProperties;
    id: string;
}
const STATUS_TAGSS: Record<number, StatusTag> = {
    '8': {
        color: '#C8F3CC',
        style: {
            color: '#ffffff',
            background: '#797979',
        },
        id: '已拒绝',
    },
    '9': {
        color: '#C8F3CC',
        style: {
            color: '#ffffff',
            background: '#79000F',
        },
        id: '待审核',
    },
    '70': {
        color: '#C8F3CC',
        style: {
            color: '#ffffff',
            background: '#FD3434',
        },
        id: '待提现',
    },
    '100': {
        color: '#C8F3CC',
        style: {
            color: '#ffffff',
            background: '#11a265',
        },
        id: '已提现',
    },
};
export const WAY_KEYMAP: Record<number, string> = {
    0: '其他',
    1: '银行卡',
    2: '微信',
    3: '支付宝',
};

type ModalBaseProps = {
    onSubmitSuccess?: () => void;
    formData?: any;
    status?: any; //规格组列表
    item?: any; //
} & Pick<ModalProps, 'afterClose' | 'title' | 'okButtonProps' | 'text' | 'open' | 'onCancel' | 'footer'> &
    Pick<GridFormProps, 'onFinish' | 'onFinishFailed' | 'form' | 'disabled'> & {
        onCancel?: (e?: React.MouseEvent<HTMLElement>) => void;
    };

const ModalBase: ForwardRefExoticComponent<RefAttributes<ModalRefAttributes> & ModalBaseProps> = forwardRef(
    (props, ref) => {
        const { open, title, form, afterClose, footer, onCancel, item, onFinish, onFinishFailed, status } = props;
        const intl = useIntl();
        const tableRef1 = useRef<UseGridTableRefAttributesReturn>(null);
        const [autoHeight] = useBoolean(true);
        // const pageSettingApi = usePageSetting({
        //     pageId: pageId,
        //     localStoragePrefix: LOCAL_STORAGE_KEY_BASE_NAME,
        // });
        const [productDataSource, setProductDataSource] = useState<any>([]);
        const [data, setData] = useState<any>({});
        const { selectOptions: selectOptionsBankAccountData } = useBankAccount();

        const columns: Array<GridTableColumnsType> = [
            {
                title: <FormattedMessage id={'操作时间'} />,
                dataIndex: 'creationTime',
                key: 'creationTime',
                align: 'center',
                width: 160,
                renderFormatter(options) {
                    return <FormatTextAuto>{timestampToDate(Number(options.value))}</FormatTextAuto>;
                },
            },
            {
                title: <FormattedMessage id={'操作人'} />,
                dataIndex: 'createdUserName',
                key: 'createdUserName',
                align: 'center',
            },
            {
                title: <FormattedMessage id={'操作ip'} />,
                dataIndex: 'ipAddress',
                key: 'ipAddress',
                align: 'center',
            },
            {
                title: <FormattedMessage id={'操作类型'} />,
                dataIndex: 'operationType',
                key: 'operationType',
                align: 'center',
                renderFormatter(options) {
                    return <FormattedMessage id={LOG_OPERATION_TYPE[options.item.operationType]} />;
                },
            },
            {
                title: <FormattedMessage id={'操作日志'} />,
                dataIndex: 'content',
                key: 'content',
                align: 'center',
                width: 300,
            },
        ];

        const TYPE_KEY: Record<number, string> = {
            0: '预存款提现',
            1: '分销佣金提现',
        };

        useEffect(() => {
            if (item) {
                const { payAccountName, paymentVoucherUrl, remark } = item;
                setData(item);
                setProductDataSource(item?.logs);
                form.setFieldValue('payAccountName', payAccountName);
                form.setFieldValue('paymentVoucherUrl', paymentVoucherUrl);
                form.setFieldValue('remark', remark);
            }
        }, [item]);

        const statusFun = (status: number) => {
            const item = STATUS_TAGSS[Number(status)] || { style: {}, id: '未知状态' };
            const { style, id } = item;

            return (
                <Tag style={style}>
                    <FormattedMessage id={id} />
                </Tag>
            );
        };

        const formConfig = GridFormCollapse.getLayoutConfig({
            type: 'modal-small',
            columnCount: 6,
        });
        //提交表单验证成功回调
        const _onFinish: GridFormProps['onFinish'] = (value) => {
            onFinish({ ...value });
        };
        //提交表单验证失败回调
        const _onFinishFailed: GridFormProps['onFinishFailed'] = (value) => {
            onFinishFailed(value);
        };

        // const { width } = Modal.getLayoutConfig({ columnCount: 4, maxColumns: formConfig.column });
        useEffect(() => {}, [status]);

        return (
            <Modal
                title={title}
                width={1180}
                open={open}
                onOk={form.submit}
                ref={ref}
                afterClose={afterClose}
                footer={footer}
                onCancel={onCancel}
            >
                <Descriptions column={3} style={{ marginBottom: 20 }}>
                    <Descriptions.Item label={intl.formatMessage({ id: '单据编号' })}>
                        {data?.businessNumber}
                        <span style={{ marginLeft: 20 }}>{statusFun(data?.status)}</span>
                    </Descriptions.Item>
                    <Descriptions.Item label={intl.formatMessage({ id: '申请时间' })}>
                        {timestampToDate(Number(data?.appliedTime))}
                    </Descriptions.Item>
                    <Descriptions.Item label={intl.formatMessage({ id: '客户名称' })}>
                        {data?.appliedName}
                    </Descriptions.Item>
                    <Descriptions.Item label={intl.formatMessage({ id: '提现类型' })}>
                        {TYPE_KEY[data?.type]}
                    </Descriptions.Item>
                    <Descriptions.Item label={intl.formatMessage({ id: '提现金额' })}>{data?.amount}</Descriptions.Item>
                    <Descriptions.Item label={intl.formatMessage({ id: '联系人' })}>
                        {data?.contactName}
                    </Descriptions.Item>
                    <Descriptions.Item label={intl.formatMessage({ id: '联系电话' })}>
                        {data?.contactPhone}
                    </Descriptions.Item>
                </Descriptions>
                <Descriptions column={3} title="提现账户">
                    <Descriptions.Item label={intl.formatMessage({ id: '提现方式' })}>
                        <FormattedMessage id={data?.way ? WAY_KEYMAP[Number(data?.way)] : '暂无'} />
                    </Descriptions.Item>
                    <Descriptions.Item label={intl.formatMessage({ id: '姓名' })}>
                        {data?.accountHolder}
                    </Descriptions.Item>
                    <Descriptions.Item label={intl.formatMessage({ id: '账号' })}>
                        {data?.accountNumber}
                    </Descriptions.Item>
                    <Descriptions.Item label={intl.formatMessage({ id: '开户银行' })}>
                        {data?.bankName}
                    </Descriptions.Item>
                    <Descriptions.Item label={intl.formatMessage({ id: '收款二维码' })}>
                        <Space>
                            {/* <QRCode type="svg" value="https://ant.design/" /> */}
                            <QRCode type="svg" value={data?.receiveQrcode} />
                        </Space>
                    </Descriptions.Item>
                </Descriptions>
                <ProSettingTable
                    autoHeight={autoHeight}
                    content={{
                        id: -1,
                        tableProps: {
                            rowKey: 'id',
                            columns: columns,
                            resizable: true,
                            readonly: true,
                            pagination: null,
                            dataSource: productDataSource,
                            ref: tableRef1,
                            disabledBackground: TABLE_BG,
                            action: {
                                options: [GRID_TABLE_ACTIONS_DELETE],
                                width: 80,
                            },
                        },
                        // foldedNavProps: null,
                    }}
                    // id={-1}
                    pageSettingApi={null}
                />
                {/* {status !== 70 && (
                    <Descriptions column={3} style={{ marginTop: 20 }}>
                        <Descriptions.Item label={intl.formatMessage({ id: '付款账户' })}>
                            {data?.payAccountName}
                        </Descriptions.Item>
                        <Descriptions.Item label={intl.formatMessage({ id: '打款凭证' })}>
                            <Space>
                                {data?.paymentVoucherUrl
                                    ?.split(',') // 后端以字符串返回数据
                                    .filter((url: string) => url.trim())
                                    .map((url: string, index: number) => (
                                        <img
                                            key={index}
                                            src={url.trim()} // 去除可能存在的空格
                                            alt="打款凭证"
                                            width={50}
                                            height={50}
                                        />
                                    ))}
                            </Space>
                        </Descriptions.Item>
                        <Descriptions.Item>
                            <div></div>
                        </Descriptions.Item>
                        <Descriptions.Item label={intl.formatMessage({ id: '备注' })}>{data?.remark}</Descriptions.Item>
                    </Descriptions>
                )} */}
                <GridFormCollapse
                    id={'form'}
                    {...formConfig}
                    form={form}
                    onFinish={_onFinish}
                    onFinishFailed={_onFinishFailed}
                    defaultCollapsed={false}
                    renderSubmitter={() => null}
                    expandIcon={() => null}
                    // disabled={disabled}
                    // initialValues={{ rechargeQuota: 0 }}
                    labelCol={{ span: 4 }}
                    // wrapperCol={{ span: 10 }}
                >
                    {status !== 9 && (
                        <GridFormCollapse.Item
                            label={<FormatTextAuto.Locales id={'付款账户'} />}
                            name="payAccountId"
                            required
                            disabled={status === 9 ? true : false}
                        >
                            <GridSelect options={selectOptionsBankAccountData} />
                        </GridFormCollapse.Item>
                    )}
                    {status !== 9 && (
                        <GridFormCollapse.Item
                            label={<FormatTextAuto.Locales id={'打印凭证'} />}
                            name="paymentVoucherUrl"
                        >
                            <>打印凭证(to do)</>
                        </GridFormCollapse.Item>
                    )}
                    <GridFormCollapse.Item
                        label={<FormatTextAuto.Locales id={'备注'} />}
                        name="remark"
                        disabled={status === 100 ? true : false}
                    >
                        <GridInput.TextArea rows={4} />
                    </GridFormCollapse.Item>
                </GridFormCollapse>
            </Modal>
        );
    },
);
ModalBase.displayName = 'ModalBase';

type ModalWithdrawBillDetailProps = {
    onSubmitSuccess?: () => void;
    item: any;
} & Pick<ModalBaseProps, 'open' | 'onCancel'>;
export const ModalWithdrawBillDetail: FC<ModalWithdrawBillDetailProps> = (props) => {
    const { onCancel, open, item } = props;
    const [form] = Form.useForm();
    const intl = useIntl();
    const [data, setData] = useState<any>({});
    const { runAsync } = useRequest(getCashoutGet, {
        manual: true,
    });

    const reset = () => {
        form.resetFields();
    };

    useEffect(() => {
        if (open) {
            init();
        }
    }, [open]);
    const init = async () => {
        try {
            const res = await runAsync(item.id, intl);
            setData(res);
        } catch (error) {}
    };

    const onFinish = async (_value: any) => {};
    const onFinishFailed = () => {};
    // const intl = useIntl();
    return (
        <ModalBase
            title={<FormattedMessage id={'提现单详情'} />}
            open={open}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            form={form}
            afterClose={reset}
            onCancel={onCancel}
            item={data}
            status={item?.status}
            footer={
                <div>
                    <Button onClick={(e) => onCancel(e)} style={{ marginLeft: 40 }}>
                        <FormattedMessage id={'取消'} />
                    </Button>
                </div>
            }
        />
    );
};

ModalWithdrawBillDetail.displayName = 'ModalWithdrawBillDetail';

type ModalWithdrawBillAuditlProps = {
    onSubmitSuccess?: () => void;
    item: any;
} & Pick<ModalBaseProps, 'open' | 'onCancel'>;
export const ModalWithdrawBillAudit: FC<ModalWithdrawBillAuditlProps> = (props) => {
    const { onCancel, open, item, onSubmitSuccess } = props;
    const [form] = Form.useForm();
    const intl = useIntl();
    const [data, setData] = useState<any>({});
    const { runAsync } = useRequest(getCashoutGet, {
        manual: true,
    });
    const { runAsync: runAsyncAudit } = useRequest(postCashoutAudit, {
        manual: true,
    });

    const reset = () => {
        form.resetFields();
    };

    useEffect(() => {
        if (open) {
            init();
        }
    }, [open]);
    const init = async () => {
        try {
            const res = await runAsync(item.id, intl);
            setData(res);
        } catch (error) {}
    };

    const onFinish = async (value: any) => {
        try {
            const params = {
                ids: [item.id],
                status: 3,
                reason: value.remark,
            };
            await runAsyncAudit(params, intl);
            onSubmitSuccess();
        } catch (error) {}
    };
    // 拒绝提现 - status = 2
    const onRejectFinish = async () => {
        try {
            const values = await form.validateFields();
            const params = {
                ids: [item.id],
                status: 2,
                reason: values.remark || '拒绝',
            };
            await runAsyncAudit(params, intl);
            onSubmitSuccess();
        } catch (error) {}
    };
    const onFinishFailed = () => {};
    return (
        <ModalBase
            title={<FormattedMessage id={'提现单详情'} />}
            open={open}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            form={form}
            afterClose={reset}
            onCancel={onCancel}
            item={data}
            status={item?.status}
            footer={
                <div>
                    <Button type="primary" onClick={form.submit}>
                        <FormattedMessage id={'审核通过'} />
                    </Button>
                    <Button onClick={onRejectFinish} style={{ marginLeft: 40 }}>
                        <FormattedMessage id={'拒绝提现'} />
                    </Button>
                    <Button onClick={(e) => onCancel(e)} style={{ marginLeft: 40 }}>
                        <FormattedMessage id={'取消'} />
                    </Button>
                </div>
            }
        />
    );
};

ModalWithdrawBillAudit.displayName = 'ModalWithdrawBillAudit';

type ModalWithdrawBillWithdrawalProps = {
    onSubmitSuccess?: () => void;
    item: any;
} & Pick<ModalBaseProps, 'open' | 'onCancel'>;
export const ModalWithdrawBillWithdrawal: FC<ModalWithdrawBillWithdrawalProps> = (props) => {
    const { onCancel, open, item, onSubmitSuccess } = props;
    const [form] = Form.useForm();
    const intl = useIntl();
    const [data, setData] = useState<any>({});
    const { runAsync } = useRequest(getCashoutGet, {
        manual: true,
    });
    const { runAsync: runCashoutEnsure } = useRequest(postCashoutEnsure, {
        manual: true,
    });

    const reset = () => {
        form.resetFields();
    };

    useEffect(() => {
        if (open) {
            init();
        }
    }, [open]);
    const init = async () => {
        try {
            const res = await runAsync(item.id, intl);
            setData(res);
        } catch (error) {}
    };

    const onFinish = async (value: any) => {
        try {
            const params = {
                ids: [item.id],
                payAccountId: value.payAccountName,
                paymentVoucherUrl: 'todo',
                confirmedReason: value.remark,
                status: 3,
            };
            await runCashoutEnsure(params, intl);
            onSubmitSuccess();
        } catch (error) {}
    };
    // 返回审核 -
    const onRejectFinish = async () => {
        try {
            const values = await form.validateFields();
            const params = {
                ids: [item.id],
                payAccountId: values.payAccountName,
                // paymentVoucherUrl: values.paymentVoucherUrl,
                paymentVoucherUrl: 'todo',
                confirmedReason: values.remark,
                status: 2,
            };
            await runCashoutEnsure(params, intl);
            onSubmitSuccess();
        } catch (error) {}
    };

    const onFinishFailed = () => {};
    // const intl = useIntl();
    return (
        <ModalBase
            title={<FormattedMessage id={'提现单详情'} />}
            open={open}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            form={form}
            afterClose={reset}
            onCancel={onCancel}
            item={data}
            status={item?.status}
            footer={
                <div>
                    <Button type="primary" onClick={form.submit}>
                        <FormattedMessage id={'确认提现'} />
                    </Button>
                    <Button onClick={onRejectFinish} style={{ marginLeft: 40 }}>
                        <FormattedMessage id={'返回审核'} />
                    </Button>
                    <Button onClick={(e) => onCancel(e)} style={{ marginLeft: 40 }}>
                        <FormattedMessage id={'取消'} />
                    </Button>
                </div>
            }
        />
    );
};

ModalWithdrawBillWithdrawal.displayName = 'ModalWithdrawBillWithdrawal';

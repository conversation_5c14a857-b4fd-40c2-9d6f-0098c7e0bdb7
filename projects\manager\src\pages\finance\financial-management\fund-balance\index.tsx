import { TABLE_BG } from '@/common';
import { PAY_WAY } from '@/common/message';
import { SelectTimeRange } from '@/components/feature/basic-infomation/select-time-range';
import { enumToSelectOptions } from '@/utils/enum-to-select-options';
import { Button, FormattedMessageSpan, GridFormCollapse, GridInput, GridSelect, Space, Text } from '@common/components';
import { LayoutProComponentRefAttributesType, LayoutTabsPro, LayoutTabsProProps } from '@common/components/feature';
import { postKindItemList } from '@common/services/api/admin/mods/finance';
import { Form, GridTableColumnsType, message } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import dayjs from 'dayjs';
import { pick } from 'lodash';
import { get as _get } from 'lodash';
import React, { FC, useRef, useState } from 'react';
import ModalFundAdjustment from './components/modal-fund-adjustment';
import { useBoolean } from 'ahooks';
import ModalTransfer from './components/modal-transfer';
import ModalBalanceAdjustment from '../balance-transactions/components/modal-balance-adjustment';

const FundBalance: FC = () => {
    const intl = useIntl();
    const [openAdjustMent, { setFalse: setAdjustMentFalse }] = useBoolean(false);
    const [openTransfer, { setTrue: setTransferTrue, setFalse: setTransferFalse }] = useBoolean(false);
    const [transferType, setTransferType] = useState<'in' | 'out'>();
    const [formData, setFormData] = useState<any>();
    const layoutProRef = useRef<LayoutProComponentRefAttributesType>(null);
    // 余额调整弹窗
    const [balanceOpen, { setTrue: setBalanceTrue, setFalse: setBalanceFalse }] = useBoolean(false);
    const [currentData, setCurrentData] = useState<any>(null);
    const columns: Array<GridTableColumnsType> = [
        {
            // title: <FormattedMessage id={'操作'} />,
            dataIndex: 'option',
            key: 'option',
            alwaysControl: true,
            ellipsis: true,
            renderControl: (option) => {
                return (
                    <Space.Group>
                        <FormattedMessageSpan
                            id={'转入'}
                            type="link"
                            onClick={() => {
                                setTransferType('in');
                                setFormData(option.item);
                                setTransferTrue();
                            }}
                        />
                        <FormattedMessageSpan
                            id={'转出'}
                            type="link"
                            onClick={() => {
                                setTransferType('out');
                                setFormData(option.item);
                                setTransferTrue();
                            }}
                        />
                        <FormattedMessageSpan id={'资金明细'} type="link" />
                        <FormattedMessageSpan
                            id={'余额调整'}
                            type="link"
                            onClick={() => {
                                // setFormData(option.item);
                                // setAdjustMentTrue();
                                setCurrentData(option.item);
                                setBalanceTrue();
                            }}
                        />
                    </Space.Group>
                );
            },
            fixed: 'first',
            align: 'center',
            summary: false,
        },
        {
            // title: <FormattedMessage id={'账户名称'} />,
            dataIndex: 'name',
            key: 'name',
            align: 'center',
            sorter: true,
            summary: false,
        },
        {
            // title: <FormattedMessage id={'所属支付方式'} />,
            dataIndex: 'typeName',
            key: 'typeName',
            align: 'center',
            sorter: true,
            summary: false,
        },

        {
            // title: <FormattedMessage id={'资金余额'} />,
            dataIndex: 'amount',
            key: 'amount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return <Text type="danger">{options.item.amount}</Text>;
            },
        },
        {
            // title: <FormattedMessage id={'本期收款笔数'} />,
            dataIndex: 'receiveCount',
            key: 'receiveCount',
            align: 'center',
            sorter: true,
        },
        {
            // title: <FormattedMessage id={'本期收款金额'} />,
            dataIndex: 'receiveAmount',
            key: 'receiveAmount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return <Text type="link">{options.item.receiveAmount}</Text>;
            },
        },
        {
            // title: <FormattedMessage id={'本期付款笔数'} />,
            dataIndex: 'payableCount',
            key: 'payableCount',
            align: 'center',
            sorter: true,
        },
        {
            // title: <FormattedMessage id={'本期付款金额'} />,
            dataIndex: 'payableAmount',
            key: 'payableAmount',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return <Text type="link">{options.item.payableAmount}</Text>;
            },
        },
        {
            // title: <FormattedMessage id={'本期发生'} />,
            dataIndex: 'balance',
            key: 'balance',
            align: 'center',
            sorter: true,
            renderFormatter(options) {
                return <Text type="link">{options.item.balance}</Text>;
            },
        },
    ];
    const [form] = Form.useForm();
    const formProps = (id: string) => {
        return {
            fieldMeta: [
                { name: 'timeRange', label: '时间', displayLabel: '时间' },
                { name: 'keywords', label: '账户', displayLabel: '账户' },
                { name: 'typeId', label: '所属支付方式', displayLabel: '所属支付方式' },
            ],
            items: [
                {
                    name: 'timeRange',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <SelectTimeRange />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'keywords',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridInput placeholder={intl.formatMessage({ id: '账户名称' })} />
                            </GridFormCollapse.Item>
                        );
                    },
                },
                {
                    name: 'typeId',
                    render(options: any) {
                        return (
                            <GridFormCollapse.Item label={options.label} name={options.name} key={options.name}>
                                <GridSelect
                                    options={enumToSelectOptions(PAY_WAY)}
                                    placeholder={intl.formatMessage({ id: '全部' })}
                                />
                            </GridFormCollapse.Item>
                        );
                    },
                },
            ],
            id: id,
            form: form,
            expandIcon: false,
            defaultCollapsed: true,
            initialValues: { timeRange: [dayjs().startOf('month'), dayjs().endOf('month')] },
        };
    };
    const tableProps = (): LayoutTabsProProps['items'][number]['tableProps'] => {
        return {
            columns: columns,
            rowKey: 'id',
            columnsMeta: [
                {
                    title: '操作',
                    displayTitle: '操作',
                    key: 'option',
                },
                {
                    title: '账户名称',
                    displayTitle: '账户名称',
                    key: 'name',
                },
                {
                    title: '所属支付方式',
                    displayTitle: '所属支付方式',
                    key: 'typeName',
                },
                {
                    title: '资金余额',
                    displayTitle: '资金余额',
                    key: 'amount',
                },
                {
                    title: '本期收款笔数',
                    displayTitle: '本期收款笔数',
                    key: 'receiveCount',
                },
                {
                    title: '本期收款金额',
                    displayTitle: '本期收款金额',
                    key: 'receiveAmount',
                },
                {
                    title: '本期付款笔数',
                    displayTitle: '本期付款笔数',
                    key: 'payableCount',
                },
                {
                    title: '本期付款金额',
                    displayTitle: '本期付款金额',
                    key: 'payableAmount',
                },
                {
                    title: '本期发生',
                    displayTitle: '本期发生',
                    key: 'balance',
                },
            ],
            defaultSortOrder: 'descend',
            defaultSortOrderField: 'id',
            sortDirections: ['ascend', 'descend', 'ascend'],
            request: async (params) => {
                try {
                    const { list, pager } = await postKindItemList({
                        ...(pick(params, ['pager', 'sorter']) as Required<Pick<typeof params, 'pager' | 'sorter'>>),
                        ...params.form,
                        startTime: params?.form?.timeRange?.[0]
                            ? (dayjs(params?.form?.timeRange[0]).unix() as any)
                            : dayjs().startOf('month').unix(),
                        endTime: params?.form?.timeRange?.[1]
                            ? (dayjs(params?.form?.timeRange[1]).unix() as any)
                            : dayjs().endOf('month').unix(),
                    });

                    return {
                        data: list,
                        total: pager.totals,
                    };
                } catch (err) {
                    console.log(err);

                    return {
                        data: [],
                        total: 0,
                    };
                }
            },

            rowSelection: {
                type: 'checkbox',
            },
            readonly: true,
            resizable: true,
            pagination: {},
            disabledBackground: TABLE_BG,
            summaryTotal(dataSource, dataIndex) {
                return {
                    value: dataIndex
                        ? dataSource.reduce((pre, cur) => pre + (Number(_get(cur, dataIndex)) || 0), 0)
                        : '',
                };
            },
        };
    };
    const actions = () => {
        return (
            <>
                <Button>
                    <FormattedMessage id="导出Excel" />
                </Button>
            </>
        );
    };
    const handleModalTrasferResult = async (type: 'ok' | 'cancle') => {
        setTransferFalse();
        if (type === 'ok') {
            try {
                layoutProRef.current?.runLoading();
                await layoutProRef.current?.refreshRequest();
                layoutProRef.current?.stopLoading();
                message.success(intl.formatMessage({ id: '操作成功' }));
            } catch (error) {
                layoutProRef.current?.stopLoading();
            }
        }
    };
    return (
        <>
            <ModalFundAdjustment
                open={openAdjustMent}
                formValue={formData}
                handleModalResult={() => setAdjustMentFalse()}
            />
            <ModalTransfer
                open={openTransfer}
                transferType={transferType}
                formValue={formData}
                handleModalResult={handleModalTrasferResult}
            />
            {/* 资金调整 */}
            <ModalBalanceAdjustment
                dataSource={currentData}
                type={606}
                open={balanceOpen}
                onCancel={() => setBalanceFalse()}
                onOk={() => {
                    setBalanceFalse();
                    layoutProRef.current?.refreshRequest();
                }}
            />
            <LayoutTabsPro
                items={[
                    {
                        label: <FormattedMessage id={'资金余额'} />,
                        key: '1',
                        action: actions(),
                        formProps: formProps('01'),
                        tableProps: tableProps(),
                        ref: layoutProRef,
                    },
                ]}
            />
        </>
    );
};

FundBalance.displayName = 'FundBalance';

export default FundBalance;

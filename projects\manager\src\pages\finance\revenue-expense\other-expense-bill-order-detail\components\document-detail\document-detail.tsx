import { ConfigGridTable, ConfigGridTableColumnsType } from '@common/components/feature';
import { UseGridTableRefAttributesReturn } from '@weway/beacon/dist/grid-table/hooks/data';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { uniqueId } from 'lodash';
import { tableSummaryTotal } from '@/utils';

type DocumentTableProps = {
    onFinish: (data: any) => void;
    datas?: any[];
    status?: number;
};
const DocumentTable = forwardRef((props: DocumentTableProps, ref) => {
    const { onFinish, datas } = props;
    const tableRef = useRef<UseGridTableRefAttributesReturn>(null);
    const [dataSource, setDataSource] = useState<any>(
        new Array(10).fill(undefined).map((_) => ({
            id: 'uuid' + uniqueId(),
        })),
    );
    const columns: Array<ConfigGridTableColumnsType<any>> = [
        {
            title: '收入编号',
            dataIndex: 'code',
            key: 'code',
            align: 'center',
            summary: false,
        },
        {
            title: '收入名称',
            dataIndex: 'name',
            key: 'name',
            summary: false,
            align: 'center',
        },
        {
            title: '路径',
            dataIndex: 'path',
            key: 'path',
            summary: false,
            align: 'center',
        },
        {
            title: '金额',
            dataIndex: 'amount',
            key: 'amount',
            align: 'center',
        },

        {
            title: '明细备注',
            dataIndex: 'remark',
            key: 'remark',
            summary: false,
            align: 'center',
        },
    ];

    useEffect(() => {
        if (datas && datas.length > 0) {
            setDataSource(datas);
        } else {
            setDataSource(
                new Array(10).fill(undefined).map((_) => ({
                    id: 'uuid' + uniqueId(),
                })),
            );
        }
    }, [datas]);
    useImperativeHandle(ref, () => ({
        submit() {
            setDataSource(tableRef.current.getDataSource());
            onFinish(tableRef.current.getDataSource());
        },
    }));

    return (
        <>
            <ConfigGridTable
                ref={tableRef}
                configId=""
                resizable
                columns={columns}
                rowKey={'code'}
                dataSource={dataSource}
                editableIcon={false}
                readonly={true}
                summaryTotal={tableSummaryTotal}
                scroll={{ y: 550 }}
            />
        </>
    );
});
DocumentTable.displayName = 'DocumentTable';
export default DocumentTable;

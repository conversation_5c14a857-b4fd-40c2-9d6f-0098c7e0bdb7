import React, { forwardRef, useState } from 'react';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { GridInput, message, Spin } from '@weway/beacon';
import { Button, FormattedMessageSpan, Space } from '@common/components';
import { useRequest } from 'ahooks';
import { postOtherAppendLog, postOtherLogList } from '@common/services/api/admin/mods/inventory';
import dayjs from 'dayjs';
import { LOG_OPERATION_TYPE } from '@/common/message';
import { DocumentDetailProps } from '../components/data';
import { ConfigGridTable, ConfigGridTableColumnsType } from '@common/components/feature';
import { GRID_FORM_LAYOUT_WIDTH } from '@/common';
/**
 * 单据详情
 * @returns
 */
const OperationLog = forwardRef((props: DocumentDetailProps, _ref: any) => {
    const { id } = props;
    const intl = useIntl();
    const columns: Array<ConfigGridTableColumnsType<any>> = [
        {
            title: '操作时间',
            dataIndex: 'creationTime',
            key: 'creationTime',
            align: 'center',
            renderFormatter(options) {
                return dayjs.unix(options.item.creationTime).format('YYYY-MM-DD HH:mm:ss');
            },
            sortDirections: ['ascend', 'descend', 'ascend'],
            sorter(a, b) {
                return a.creationTime - b.creationTime;
            },
        },
        {
            title: '操作人',
            dataIndex: 'createdName',
            key: 'createdName',
            align: 'center',
            renderFormatter(options) {
                return (
                    <Space direction="vertical">
                        {options.item.createdName}
                        <FormattedMessageSpan type="subText" id={options.item.createdUserName || 'unknown'} />
                    </Space>
                );
            },
        },
        {
            title: '操作ip',
            dataIndex: 'ipAddress',
            key: 'ipAddress',
        },
        {
            title: '操作类型',
            dataIndex: 'operationType',
            key: 'operationType',
            renderFormatter(options) {
                return <FormattedMessage id={LOG_OPERATION_TYPE[options.item.operationType] || '--'} />;
            },
        },
        {
            title: '操作日志',
            dataIndex: 'content',
            key: 'content',
        },
    ];
    const { data, loading, refreshAsync } = useRequest(() => {
        return postOtherLogList({
            id: id,
            type: 303,
            pager: {
                size: 999,
                index: 1,
            },
            sorter: [
                {
                    member: 'id',
                    mode: 0,
                },
            ],
        });
    });
    const { runAsync, loading: loadingAdd } = useRequest(postOtherAppendLog, {
        manual: true,
    });
    const [content, setContent] = useState('');
    return (
        <Spin spinning={loading || loadingAdd}>
            <Space direction="vertical">
                <ConfigGridTable
                    configId=""
                    resizable
                    columns={columns}
                    rowKey={'id'}
                    readonly={true}
                    dataSource={data?.list || []}
                    editableIcon={false}
                    align="center"
                    disabledBackground="#fff"
                />
                <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'end' }}>
                    <FormattedMessage id="追加日志:" />
                    <GridInput.TextArea
                        style={{ width: GRID_FORM_LAYOUT_WIDTH, marginLeft: 10, marginRight: 10 }}
                        onChange={(e) => {
                            setContent(e.target.value);
                        }}
                        value={content}
                    />
                    <Button
                        type="primary"
                        onClick={async () => {
                            try {
                                await runAsync({ id: id, content: content }, intl);
                                setContent('');
                                message.success(intl.formatMessage({ id: '日志追加成功' }));
                                refreshAsync();
                            } catch (error) {}
                        }}
                    >
                        <FormattedMessage id="确认追加" />
                    </Button>
                </div>
            </Space>
        </Spin>
    );
});

export default OperationLog;
OperationLog.displayName = 'OperationLog';

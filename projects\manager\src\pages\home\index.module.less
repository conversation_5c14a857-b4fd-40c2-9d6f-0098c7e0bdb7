.home {
    display: flex;

    &-left {
        margin-right: 10px;
        flex-grow: 2;
        flex-basis: 0;

        &-stayDo {
            &-title {
                font-size: 16px;
            }

            &-content {
                display: flex;
                /* 创建五个等宽的列 */
                flex-basis: 0;
                align-items: center;
                /* 可选，设置列之间的间距 */
                height: 100px;

                &-item {
                    text-align: center;
                    flex-grow: 1;
                    padding: 20px 0;

                    &:hover {
                        background-color: #f5f5f5;
                    }

                    > div:first-child {
                        font-size: 30px;
                        font-weight: 400;
                    }

                    > div:last-child {
                        color: #777;
                    }
                }
            }
        }

        &-todayInfo {
            display: flex;
            flex-basis: 0;
            width: 100%;
            margin: 10px 0;

            &-item {
                flex-grow: 1;
                margin-right: 10px;
                display: flex;
                justify-content: space-between;

                &-left {
                    text-align: center;
                    flex-grow: 3;

                    &-value {
                        font-size: 30px;
                        font-weight: 400;
                    }
                }

                &-right {
                    height: 60px;
                    width: 60px;
                    // background-color: #777;
                }

                &:last-child {
                    margin-right: 0;
                }
            }

            &-quantity {
                .home-left-todayInfo-item-left-type {
                    color: red;
                }
            }

            &-newCustomer {
                .home-left-todayInfo-item-left-type {
                    color: #1793ba;
                }
            }

            &-orderAmount {
                .home-left-todayInfo-item-left-type {
                    color: #f3633a;
                }
            }
        }
    }

    &-right {
        flex-grow: 1;
        flex-basis: 0;

        &-systemInfo {
            color: #666;
        }

        &-apply {
            margin: 10px 0;

            &-content {
                &-item {
                    display: flex;
                    align-items: center;

                    &-input {
                        width: 50%;
                        margin: 0 5%;
                    }

                    &-img {
                        display: inline-block;
                        width: 63px;
                        text-align: center;
                    }
                }

                &-app {
                    margin: 15px 0;

                    img {
                        height: 30px;
                    }
                }

                &-appOrVx {
                    display: flex;
                    box-sizing: border-box;
                    padding: 0 30px;
                    justify-content: space-between;
                    width: 70%;
                    margin: 0 auto;

                    &-item {
                        text-align: center;

                        span {
                            display: block;
                        }
                    }

                    &-title {
                        color: #666;
                        display: flex;
                        justify-content: space-between;

                        > div {
                            text-align: center;
                        }
                    }
                }
            }
        }

        &-invite {
            padding-bottom: 30px !important;

            &-content {
                color: #666;
                font-size: 12px;

                &-item {
                    display: flex;
                    align-items: center;

                    &-title {
                        min-width: 55px;
                        max-width: 65px;
                    }
                }

                &-methodOne {
                    margin-bottom: 20px;

                    &-code {
                        color: #f3633a;
                    }
                }

                &-copy {
                    margin-top: 10px;

                    &-box {
                        flex-grow: 1;
                        white-space: nowrap;

                        input {
                            max-width: 66%;
                            margin-right: 5%;
                        }
                    }
                }
            }
        }
    }
}

.card {
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 0 4px 0px #eee;
    border: #eee 1px solid;
}

.cardLayout {
    padding: 10px;
    border-radius: 10px;
    box-shadow: 0 0 4px 0px #eee;
    border: #eee 1px solid;
    background-color: #fff;
}

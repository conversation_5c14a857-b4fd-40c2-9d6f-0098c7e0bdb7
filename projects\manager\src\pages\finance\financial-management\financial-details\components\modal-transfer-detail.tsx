import { Button, FormattedMessageSpan, FormatTextAuto, Modal, Text } from '@common/components';
import { Input, Space, Spin } from '@weway/beacon';
import React, { useEffect, useRef } from 'react';
import { ReadOnlyDataCard } from '@/components/feature/read-only-data-card';
import { ConfigGridTable } from '@common/components/feature';
import { UseGridTableRefAttributesReturn } from '@weway/beacon/dist/grid-table/hooks/data';
import { useRequest } from 'ahooks';
import { useIntl } from '@weway/i18n';
import { getTransferBillGet } from '@common/services/api/admin/mods/finance';
import dayjs from 'dayjs';
/**
 * 转账单详情
 * @returns
 */
type ModalTransferDetailProps = {
    open: boolean;
    id: number;
    onClose: () => void;
};
const ModalTransferDetail = (props: ModalTransferDetailProps) => {
    const { open, onClose, id } = props;

    const intl = useIntl();

    const tableRef = useRef<UseGridTableRefAttributesReturn>(null);

    const handleOk = async () => {};

    const handleCancel = () => {};
    const { runAsync, data } = useRequest(getTransferBillGet, {
        manual: true,
    });
    const init = async () => {
        try {
            await runAsync(id, intl);
        } catch (error) {}
    };
    useEffect(() => {
        if (open) {
            init();
        }
    }, [open]);
    return (
        <Modal
            title={<FormattedMessageSpan id="转账单详情" />}
            open={open}
            width={'1180'}
            onOk={handleOk}
            onCancel={handleCancel}
            footer={
                <Space>
                    <Button onClick={onClose}>
                        <FormattedMessageSpan id="取消(Esc)" />
                    </Button>
                </Space>
            }
            destroyOnClose
        >
            <Spin spinning={false}>
                <ReadOnlyDataCard
                    style={{ marginBottom: 10 }}
                    dataSource={{
                        businessNumber: data?.businessNumber,
                        businessDate: dayjs.unix(data?.businessDate as any).format('YYYY-MM-DD HH:mm:ss'),
                        amount: data?.amount,
                        fromAccountName: data?.fromAccountName,
                        toAccountName: data?.toAccountName,
                        handlerName: data?.handlerName,
                        departmentName: data?.departmentName,
                        remark: data?.remark,
                    }}
                    columns={[
                        {
                            title: <FormatTextAuto.Locales id="单据编号" />,
                            key: 'businessNumber',
                            span: 8,
                            render: (option) => {
                                return <Text type="primary">{option.value}</Text>;
                            },
                        },
                        {
                            title: <FormatTextAuto.Locales id="转款时间" />,
                            key: 'businessDate',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="转款金额" />,
                            key: 'amount',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="转出账户" />,
                            key: 'fromAccountName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="转入账户" />,
                            key: 'toAccountName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="经手人" />,
                            key: 'handlerName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="部门" />,
                            key: 'departmentName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="备注" />,
                            key: 'remark',
                            span: 8,
                        },
                    ]}
                />
                <ConfigGridTable
                    ref={tableRef}
                    configId=""
                    resizable
                    columns={[
                        {
                            title: '操作',
                            dataIndex: 'options',
                            key: 'options',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作时间',
                            dataIndex: 'creationTime',
                            key: 'creationTime',
                            align: 'center',
                            type: 'date-picker',
                            summary: false,
                        },
                        {
                            title: '操作人',
                            dataIndex: 'createdName',
                            key: 'createdName',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作ip',
                            dataIndex: 'ipAddress',
                            key: 'ipAddress',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作类型',
                            dataIndex: 'operationType',
                            key: 'operationType',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作日志',
                            dataIndex: 'content',
                            key: 'content',
                            align: 'center',
                            summary: false,
                        },
                    ]}
                    rowKey={'code'}
                    dataSource={data?.items || []}
                    editableIcon={false}
                    readonly={true}
                    scroll={{ y: 300 }}
                />
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span>
                        <FormattedMessageSpan id="追加日志" />：
                    </span>
                    <Input.TextArea style={{ width: 400, marginRight: 10 }} />
                    <Button type="primary">
                        <FormattedMessageSpan id="确认追加" />
                    </Button>
                </div>
            </Spin>
        </Modal>
    );
};
ModalTransferDetail.displayName = 'ModalTransferDetail';
export default ModalTransferDetail;

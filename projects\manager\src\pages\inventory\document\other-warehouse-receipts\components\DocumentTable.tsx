import { ConfigGridTableColumnsType, SettingTable } from '@common/components/feature';
import { GRID_TABLE_ACTIONS_ADD, GRID_TABLE_ACTIONS_DELETE, Image } from '@weway/beacon';
import { UseGridTableRefAttributesReturn } from '@weway/beacon/dist/grid-table/hooks/data';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { postSkuSelectorList } from '@common/services/api/admin/mods/product';
import { uniqueId } from 'lodash';
import { documentLineDataCalculate } from '@/utils/data-calculate';
import { EditOutlined } from '@ant-design/icons';
import { useBoolean } from 'ahooks';
import ModalEditQty from '../../other-outbound-orders/components/modal-edit-qty';
import { kmCalculate, kmUnitConvertQtyText, kmUnitConvertRelation } from '@common/utils/index';
import { DefaultRow } from '@/utils/data-source';
import { tableSummaryTotal } from '@/utils';
import { BASE_NAME } from '@/common';
import { usePageSetting } from '@common/hooks/usePageSetting';
import { useRouteMeta } from '@common/hooks/useRouteMeta';
import { Space, Text } from '@common/components';
type DocumentTableProps = {
    onFinish: (data: any) => void;
    datas?: any[];
    depotId: number;
    onChangeTable: (data: Array<any>) => void;
};
const DocumentTable = forwardRef((props: DocumentTableProps, ref: any) => {
    const { onFinish, datas, depotId, onChangeTable } = props;
    const tableRef = useRef<UseGridTableRefAttributesReturn>(null);
    const depotIdRef = useRef(depotId);
    const { configId: pageId } = useRouteMeta();

    const pageSettingApi = usePageSetting({
        pageId: pageId,
        localStoragePrefix: BASE_NAME,
    });
    const [dataSource, setDataSource] = useState<any>(
        new Array(1).fill(undefined).map((_) => ({
            id: 'uuid' + uniqueId(),
        })),
    );
    const [qtyModalOpen, { setTrue: setQtyModalOpenTrue, setFalse: setQtyModalOpenFalse }] = useBoolean(false);
    const columns: Array<ConfigGridTableColumnsType<any>> = [
        {
            title: '商品编号',
            dataIndex: 'spuSn',
            key: 'spuSn',
            alwaysControl: true,
            ellipsis: false,

            type: 'action-input',
            readonly: false,
            summary: false,
            sortDirections: ['ascend', 'descend', 'ascend'],
            sorter(a, b) {
                return a.spuSn.localeCompare(b.spuSn);
            },

            actionInputProps(options) {
                const { rowKey, item = {} } = options;
                return {
                    selectProps: {
                        onChange(value) {
                            const unit = value?.units?.find?.((item: any) => item?.isDefaultSale) || value?.units?.[0];
                            const newItem = {
                                ...item,
                                ...DefaultRow,
                                ...value,
                                spuName: value?.name,
                                skuId: value?.id,
                                unitName: unit?.unitName || '',
                                unitRate: unit?.unitRate || 1,
                                unitType: unit?.unitType || 0, //选中默认单位
                                qty: 1,
                                taxRate: 0,
                                pictureSrc: '图片',
                                basicQty: kmCalculate(1, unit?.unitRate || 1, 'multiply'),
                                stock: kmCalculate(value?.stock, unit?.unitRate || 1, 'divide'),
                                baseStock: value?.stock, //基础账面库存
                                originalPrice: unit?.costPrice || 1,
                                id: 'uuid' + uniqueId(),
                                barcode: unit?.barcode || value?.barcode, //先取单位的barcode，如果单位没有barcode，取spu的barcode
                            };
                            const data = documentLineDataCalculate({
                                key: 'qty',
                                value: 1,
                                dataSource: newItem,
                            });
                            tableRef.current.update(rowKey, data);
                        },
                        style: {
                            width: 400,
                            height: 300,
                        },
                        columns: [
                            {
                                title: '商品编号',
                                dataIndex: 'spuSn',
                            },
                            {
                                title: '商品名称',
                                dataIndex: 'name',
                            },
                        ],
                        rowKey: 'id',
                        request: async (search = '') => {
                            try {
                                const { list } = await postSkuSelectorList({
                                    nameOrCode: search,
                                    isLoadBookStock: true,
                                    depotId: depotIdRef?.current,
                                    pager: {
                                        index: 1,
                                        size: 999,
                                    },
                                    sorter: [
                                        {
                                            member: 'name',
                                            mode: 0,
                                        },
                                    ],
                                });
                                return list;
                            } catch (error) {
                                return [];
                            }
                        },
                    },
                };
            },
        },
        {
            title: '商品名称',
            dataIndex: 'name',
            key: 'name',
            summary: false,
            alwaysControl: true,
            ellipsis: false,
            type: 'action-input',
            readonly: false,
            sortDirections: ['ascend', 'descend', 'ascend'],
            sorter(a, b) {
                return a.name.localeCompare(b.name);
            },
            actionInputProps(options) {
                const { rowKey, item = {} } = options;
                return {
                    selectProps: {
                        onChange(value) {
                            const unit = value?.units?.find?.((item: any) => item?.isDefaultSale) || value?.units?.[0];
                            const newItem = {
                                ...item,
                                ...DefaultRow,
                                ...value,
                                spuName: value?.name,
                                skuId: value?.id,
                                unitName: unit?.unitName || '',
                                unitRate: unit?.unitRate || 1,
                                unitType: unit?.unitType || 0, //选中默认单位
                                qty: 1,
                                taxRate: 0,
                                pictureSrc: '图片',
                                baseStock: value?.stock, //基础账面库存
                                basicQty: kmCalculate(1, unit?.unitRate || 1, 'multiply'),
                                stock: kmCalculate(value?.stock, unit?.unitRate || 1, 'divide'),
                                originalPrice: unit?.costPrice || 1,
                                id: 'uuid' + uniqueId(),
                                barcode: unit?.barcode || value?.barcode, //先取单位的barcode，如果单位没有barcode，取spu的barcode
                            };
                            const data = documentLineDataCalculate({
                                key: 'qty',
                                value: 1,
                                dataSource: newItem,
                            });

                            tableRef.current.update(rowKey, data);
                        },
                        style: {
                            width: 400,
                            height: 300,
                        },
                        columns: [
                            {
                                title: '商品编号',
                                dataIndex: 'spuSn',
                            },
                            {
                                title: '商品名称',
                                dataIndex: 'name',
                            },
                        ],
                        rowKey: 'id',
                        request: async (search = '') => {
                            try {
                                const { list } = await postSkuSelectorList({
                                    nameOrCode: search,
                                    depotId: depotIdRef?.current,
                                    isLoadBookStock: true,
                                    pager: {
                                        index: 1,
                                        size: 999,
                                    },
                                    sorter: [
                                        {
                                            member: 'name',
                                            mode: 0,
                                        },
                                    ],
                                });
                                return list;
                            } catch (error) {
                                return [];
                            }
                        },
                    },
                };
            },
        },
        {
            title: '图片',
            dataIndex: 'pictureSrc',
            key: 'pictureSrc',
            summary: false,
            align: 'center',
            renderFormatter(options) {
                return (
                    options.value && (
                        <Image src={options.value as string} style={{ width: 50, height: 50, padding: 5 }} />
                    )
                );
            },
        },
        {
            title: '规格',
            dataIndex: 'specValue',
            key: 'specValue',
            summary: false,
        },
        {
            title: '条码',
            dataIndex: 'barcode',
            key: 'barcode',
            summary: false,
        },
        {
            title: '品牌',
            dataIndex: 'brandName',
            key: 'brandName',
            summary: false,
        },

        {
            title: '账面库存',
            dataIndex: 'stock',
            key: 'stock',
            summary: false,
        },
        {
            title: () => {
                return (
                    <Space>
                        <Text>数量</Text>
                        <EditOutlined onClick={() => setQtyModalOpenTrue()} />
                    </Space>
                );
            },
            dataIndex: 'qty',
            key: 'qty',
            type: 'input-number',
            readonly: false,
            suffix: <EditOutlined onClick={() => setQtyModalOpenTrue()} />,
            onChange: (params) => {
                const { rowKey, item, value } = params;
                const data = documentLineDataCalculate({
                    key: 'qty',
                    value: value,
                    dataSource: item,
                });
                data.basicQty = kmCalculate(Number(value), Number(item?.unitRate || 1), 'multiply');
                tableRef.current.update(rowKey, data);
            },
        },
        {
            title: '单位',
            dataIndex: 'unitType',
            key: 'unitType',
            type: 'select',
            readonly: false,
            alwaysControl: true,
            summary: false,
            align: 'center',
            options(options) {
                return options?.item?.units?.map((item: any) => ({
                    ...item,
                    label: item.unitName,
                    value: item.unitType,
                }));
            },

            onChange: (options) => {
                const { item = {}, value, rowKey } = options;
                if (Array.isArray(item?.units)) {
                    const findUnit = item?.units?.find((unitItem: any) => {
                        return unitItem.unitType === value;
                    });
                    if (findUnit) {
                        item.priceBeforeDiscount = findUnit?.costPrice || 1;
                        item.originalPrice = findUnit?.costPrice || 1;
                        item.unitName = findUnit.unitName;
                        item.unitRate = findUnit.unitRate;
                        item.unitType = findUnit.unitType;
                        item.basicQty = kmCalculate(item?.qty || 0, findUnit?.unitRate || 1, 'multiply');
                        //计算账面库存
                        item.stock = kmCalculate(item?.baseStock, item?.unitRate || 1, 'divide');
                        //获取条码
                        item.barcode = findUnit?.barcode || item?.barcode;
                        const data = documentLineDataCalculate({
                            key: 'qty',
                            value: item?.qty,
                            dataSource: item,
                        });

                        tableRef.current.update(rowKey, data);
                    }
                }
            },
        },
        {
            title: '换算关系',
            dataIndex: 'unitConvertRelation',
            key: 'unitConvertRelation',
            readonly: true,
            align: 'center',
            summary: false,
            width: 200,
            renderFormatter: (options) => {
                const { item = {} } = options;
                return kmUnitConvertRelation(item?.units || []);
            },
        },
        {
            title: '换算结果',
            dataIndex: 'unitConvertQtyText',
            key: 'unitConvertQtyText',
            readonly: true,
            align: 'center',
            summary: false,
            renderFormatter: (options) => {
                const { item = {} } = options;
                const basicQty = item?.basicQty || 0;
                const unitConvertQtyText = kmUnitConvertQtyText(basicQty, item?.units || []);
                return unitConvertQtyText;
            },
        },
        {
            title: '基础数量',
            dataIndex: 'basicQty',
            key: 'basicQty',
            align: 'center',
            type: 'input-number',
            readonly: false,
            onChange: (params) => {
                const { rowKey, item, value } = params;
                if (Array.isArray(item?.units)) {
                    const findUnit = item?.units?.find((unitItem: any) => {
                        return unitItem.unitType === item?.unitType;
                    });
                    if (findUnit) {
                        item.qty = kmCalculate(Number(value), Number(findUnit?.unitRate || 1), 'divide');
                        const data = documentLineDataCalculate({
                            key: 'qty',
                            value: item.qty,
                            dataSource: item,
                        });
                        data.basicQty = Number(value);
                        tableRef.current.update(rowKey, data);
                    }
                }
            },
        },
        {
            title: '基础单位',
            dataIndex: 'basicUnitName',
            key: 'basicUnitName',
            summary: false,
        },
        {
            title: '单价',
            dataIndex: 'price',
            key: 'price',
            type: 'input-number',
            readonly: false,
            summary: false,
            onChange: (params) => {
                const { rowKey, item, value } = params;
                const data = documentLineDataCalculate({
                    key: 'originalPrice',
                    value: value,
                    dataSource: item,
                });
                tableRef.current.update(rowKey, data);
            },
        },
        {
            title: '金额',
            dataIndex: 'amount',
            key: 'amount',
            type: 'input-number',
            readonly: false,
            onChange: (params) => {
                const { rowKey, item, value } = params;
                const data = documentLineDataCalculate({
                    key: 'originalAmount',
                    value: value,
                    dataSource: item,
                });
                tableRef.current.update(rowKey, data);
            },
        },
        {
            title: '折扣',
            dataIndex: 'discountRate',
            key: 'discountRate',
            type: 'input-number',
            readonly: false,
            summary: false,
            onChange: (params) => {
                const { rowKey, item, value } = params;
                const data = documentLineDataCalculate({
                    key: 'discountRate',
                    value: value,
                    dataSource: item,
                });
                tableRef.current.update(rowKey, data);
            },
        },
        {
            title: '折后单价',
            dataIndex: 'discountedPrice',
            key: 'discountedPrice',
            type: 'input-number',
            readonly: false,
            summary: false,
            onChange: (params) => {
                const { rowKey, item, value } = params;
                const data = documentLineDataCalculate({
                    key: 'discountedPrice',
                    value: value,
                    dataSource: item,
                });
                tableRef.current.update(rowKey, data);
            },
        },
        {
            title: '折后金额',
            dataIndex: 'discountedAmount',
            key: 'discountedAmount',
            type: 'input-number',
            readonly: false,

            onChange: (params) => {
                const { rowKey, item, value } = params;
                const data = documentLineDataCalculate({
                    key: 'discountedAmount',
                    value: value,
                    dataSource: item,
                });
                tableRef.current.update(rowKey, data);
            },
        },
        {
            title: '明细备注',
            dataIndex: 'remark',
            key: 'remark',

            summary: false,
            readonly: false,
            async onBlur(options) {
                const value = options.value;

                const newData = tableRef.current.getDataSource();
                newData[options.rowIndex].remark = value;
                setDataSource(newData);
            },
        },
    ];
    useEffect(() => {
        if (datas && datas.length > 0) {
            // const length = datas.length;
            // let list: any = [];
            // if (length < 10) {
            //     list = new Array(10 - length).fill(undefined).map((_) => ({
            //         id: 'uuid' + uniqueId(),
            //     }));
            // }
            setDataSource([
                ...datas.map((item: any) => {
                    return {
                        ...item,
                        name: item.spuName,
                        sn: item.spuSn,
                        baseStock: item?.beforeStock, //基础账面库存
                        stock: kmCalculate(item?.beforeStock, item?.unitRate || 1, 'divide'),
                    };
                }),
                // ...list,
            ]);
        } else {
            setDataSource(
                new Array(1).fill(undefined).map((_) => ({
                    id: 'uuid' + uniqueId(),
                })),
            );
        }
    }, [datas]);
    useEffect(() => {
        depotIdRef.current = depotId;
    }, [depotId]);

    useImperativeHandle(ref, () => ({
        submit() {
            setDataSource(tableRef.current.getDataSource());
            onFinish(tableRef.current.getDataSource());
        },
    }));
    return (
        <>
            {/* 修改整单数量 */}
            <ModalEditQty
                open={qtyModalOpen}
                onOk={(values) => {
                    const tableDataSource = tableRef.current.getDataSource();
                    const dataSource: any[] = [];
                    tableDataSource.forEach((item) => {
                        if (item.skuId) {
                            const data = documentLineDataCalculate({
                                key: 'qty',
                                value: values.qty,
                                dataSource: item,
                            });
                            data.basicQty = kmCalculate(Number(values.qty), Number(item?.unitRate || 1), 'multiply');
                            dataSource.push(data);
                        } else {
                            dataSource.push(item);
                        }
                    });
                    setDataSource?.([...dataSource]);
                    setQtyModalOpenFalse();
                }}
                onCancel={() => {
                    setQtyModalOpenFalse();
                }}
            />
            <SettingTable
                ref={tableRef}
                resizable
                columns={columns}
                rowKey={'id'}
                readonly={true}
                align="center"
                dataSource={dataSource}
                action={{
                    options: [GRID_TABLE_ACTIONS_ADD, GRID_TABLE_ACTIONS_DELETE],
                    width: 80,
                }}
                editableIcon={false}
                summaryTotal={tableSummaryTotal}
                scroll={{ y: 550 }}
                id={pageId}
                pageSettingApi={pageSettingApi}
                onValuesChange={(params) => {
                    onChangeTable(params);
                }}
                // onBeforeDelete={(options) => {
                //     const bool = tableRef.current.getDataSource().length > 1;
                //     debugger
                //     if (bool) return true;

                //     setDataSource(
                //         new Array(1).fill(undefined).map((_) => ({
                //             id: 'uuid' + uniqueId(),
                //         })),
                //     );
                //     return false;
                // }}
            />
        </>
    );
});

export default DocumentTable;
DocumentTable.displayName = 'DocumentTable';

import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { DocumentDetailProps } from './data';
import { ConfigGridForm } from '@common/components/feature';
import { useIntl } from '@weway/i18n';
import { GridDatePicker, GridForm, GridInput } from '@weway/beacon';
import { FormatTextAuto, GridFormCollapse, Space, Text } from '@common/components';
import Footer from '../footer';
import DocumentTable from './document-detail';
import dayjs from 'dayjs';
import { postLatestcodeGet } from '@common/services/api/admin/mods/basic';
import { useRequest } from 'ahooks';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
import { SelectTrader } from '@/components/feature/basic-infomation/select-trader';
import { SelectDepartment } from '@/components/feature/basic-infomation/select-department';
/**
 * 单据详情
 * @returns
 */

const DocumentDetail = forwardRef((props: DocumentDetailProps, ref) => {
    const { getConfigSettingContainer, onFinish, datas } = props;

    const intl = useIntl();
    const tabRef = useRef<any>();
    const footerRef = useRef<any>();
    const [form] = GridForm.useForm();
    const submitData: any = {
        formData: {},
        tableData: [],
        footerData: {},
    };

    const [businessNumber, setBusinessNumber] = useState<string>();
    //生成单据编号
    const { runAsync: getCode } = useRequest(postLatestcodeGet, { manual: true });
    const handleInitCode = async () => {
        const data = await getCode({ type: 202 }, intl);
        setBusinessNumber(data?.value);
    };
    useEffect(() => {
        console.log('详情', datas);
        if (datas?.id) {
            const newData = {
                ...datas,
                businessDate: dayjs.unix(datas?.businessDate),
            };
            form.setFieldsValue(newData);
        } else {
            if (datas?.businessNumber) {
                form.setFieldValue('businessNumber', datas?.businessNumber); // 单据编号
                form.setFieldValue('businessDate', datas?.businessDate); // 单据日期
            } else {
                handleInitCode();
            }
        }
    }, [datas]);

    useImperativeHandle(ref, () => ({
        async submit() {
            form.submit();
        },
    }));

    return (
        <>
            <ConfigGridForm
                getConfigSettingContainer={getConfigSettingContainer}
                labelCol={{ span: 8 }}
                form={form}
                configId=""
                renderSubmitter={() => null}
                expandIcon={false}
                defaultCollapsed={true}
                onFinish={(values: any) => {
                    tabRef.current.submit();

                    footerRef.current.submit();
                    submitData.formData = values;
                    onFinish(submitData);
                }}
                initialValues={{
                    submittedTime: dayjs(),
                }}
                disabled={datas?.status === 60 || datas?.status === 65 || datas?.status === 70}
                formItems={[
                    {
                        name: 'businessNumber',
                        label: '单据编号',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                >
                                    <Space>
                                        <GridFormCollapse.Item name={options.name}>
                                            <Text type="primary">{datas?.businessNumber ?? businessNumber}</Text>
                                        </GridFormCollapse.Item>
                                    </Space>
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'businessDate',
                        label: '单据日期',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择日期' }) }]}
                                >
                                    <GridDatePicker style={{ width: '100%' }} />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'traderName',
                        label: '往来单位',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                    required
                                >
                                    <Space>
                                        <GridFormCollapse.Item
                                            name={options.name}
                                            rules={[
                                                {
                                                    required: true,
                                                    message: intl.formatMessage({ id: '请选择往来单位' }),
                                                },
                                            ]}
                                        >
                                            <SelectTrader.Single
                                                onChange={(event: any) => {
                                                    form.setFieldsValue({
                                                        traderId: event.target?.dataSource?.id,
                                                    });
                                                }}
                                            />
                                        </GridFormCollapse.Item>
                                        <GridFormCollapse.Item name={'traderId'} hidden />
                                    </Space>
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'handlerId',
                        label: '经手人',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择经手人' }) }]}
                                >
                                    <SelectUser />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'departmentId',
                        label: '部门',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                >
                                    <SelectDepartment />
                                </GridFormCollapse.Item>
                            );
                        },
                    },

                    {
                        name: 'brief',
                        label: '单据说明',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                >
                                    <GridInput disabled={datas?.status === 70} />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                ]}
            />
            <div style={{ marginTop: 10, marginBottom: 10 }}>
                <DocumentTable
                    ref={tabRef}
                    datas={datas?.items}
                    status={datas?.status}
                    onFinish={(tableData) => {
                        submitData.tableData = tableData;
                    }}
                />
            </div>
            <Footer
                ref={footerRef}
                datas={datas}
                onFinish={(values: any) => {
                    submitData.footerData = values;
                }}
            />
        </>
    );
});
DocumentDetail.displayName = 'DocumentDetail';
export default DocumentDetail;

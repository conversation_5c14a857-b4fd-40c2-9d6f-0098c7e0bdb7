import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { DocumentDetailProps } from './data';
import { ConfigGridForm } from '@common/components/feature';
import { useIntl } from '@weway/i18n';
import { GridDatePicker, GridForm, GridInput } from '@weway/beacon';
import { FormatTextAuto, GridFormCollapse, Space, GridSelect, Tag, Text } from '@common/components';
import DocumentTable from './DocumentTable';
import dayjs from 'dayjs';
import { SelectTrader } from '@/components/feature/basic-infomation/select-trader';
import { SelectDepartment } from '@/components/feature/basic-infomation/select-department';
import { SelectDepot } from '@/components/feature/basic-infomation/select-depot';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
import Footer from '../../other-warehouse-receipts/components/footer';
/**
 * 单据详情
 * @returns
 */
const DocumentDetail = forwardRef((props: DocumentDetailProps, ref: any) => {
    const { getConfigSettingContainer, onFinish, datas, isCopy } = props;
    const intl = useIntl();
    const tabRef = useRef<any>();
    const footerRef = useRef<any>();
    const [form] = GridForm.useForm();
    const depotId = GridForm.useWatch('depotId', form);
    const [tableChangeData, setTableChangeData] = useState<Array<any>>(datas?.items || []);
    const submitData: any = {
        formData: {},
        tableData: [],
        footerData: {},
    };

    useEffect(() => {
        if (!datas?.id) {
            //处理点击新增按钮时，表单重置
            form.resetFields();
        }
        form.setFieldsValue(datas);
    }, [datas]);

    useImperativeHandle(ref, () => ({
        async submit() {
            form.submit();
        },
    }));
    return (
        <>
            <ConfigGridForm
                getConfigSettingContainer={getConfigSettingContainer}
                labelCol={{ span: 8 }}
                form={form}
                configId=""
                onFinish={(values: any) => {
                    tabRef.current.submit();

                    footerRef.current.submit();

                    submitData.formData = values;
                    onFinish(submitData);
                }}
                initialValues={{
                    businessDate: dayjs(),
                }}
                defaultCollapsed={true}
                renderSubmitter={() => null}
                formItems={[
                    {
                        name: 'businessNumber',
                        label: '单据编号',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                >
                                    <Space>
                                        <Text type="primary">{datas?.businessNumber}</Text>
                                        {datas?.id && !isCopy && <Tag type="Draft" />}
                                    </Space>
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'businessDate',
                        label: '单据日期',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择日期' }) }]}
                                >
                                    <GridDatePicker style={{ width: '100%' }} />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'traderName',
                        label: '往来单位',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                >
                                    <Space>
                                        <GridFormCollapse.Item name={options.name}>
                                            <SelectTrader.Single
                                                onChange={(event: any) => {
                                                    form.setFieldsValue({
                                                        traderId: event.target?.dataSource?.id,
                                                    });
                                                }}
                                            />
                                        </GridFormCollapse.Item>
                                        <GridFormCollapse.Item name={'traderId'} hidden />
                                    </Space>
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'handlerId',
                        label: '经手人',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    column={1}
                                    name={options.name}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择经手人' }) }]}
                                >
                                    <SelectUser
                                        placeholder={intl.formatMessage({ id: '请选择' })}
                                        onChange={(_e, obj: any) => {
                                            //选择经手人，部门自动填充
                                            form.setFieldValue('departmentId', obj?.departmentId);
                                        }}
                                    />
                                </GridFormCollapse.Item>
                            );
                        },
                    },

                    {
                        name: 'departmentId',
                        label: '部门',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                >
                                    <SelectDepartment />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'depotId',
                        label: '仓库',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    column={1}
                                    name={options.name}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择仓库' }) }]}
                                >
                                    <SelectDepot />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'deliveryMode',
                        label: '承运方式',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    column={1}
                                    name={options.name}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                >
                                    <GridSelect
                                        options={[
                                            {
                                                label: intl.formatMessage({ id: '商家配送' }),
                                                value: 3,
                                            },
                                            {
                                                label: intl.formatMessage({ id: '物流快递' }),
                                                value: 1,
                                            },
                                            {
                                                label: intl.formatMessage({ id: '上门自提' }),
                                                value: 2,
                                            },
                                        ]}
                                        placeholder={intl.formatMessage({ id: '请选择' })}
                                    />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'deliveryName',
                        label: '承运单位',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                >
                                    <GridInput />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'deliveryPhone',
                        label: '承运电话',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                >
                                    <GridInput />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'brief',
                        label: '单据说明',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                >
                                    <GridInput />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'packQty',
                        label: '打包件数',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormatTextAuto.Locales id={options.displayLabel} />}
                                >
                                    <GridInput />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                ]}
            />
            <div style={{ marginTop: 10, marginBottom: 10 }}>
                <DocumentTable
                    ref={tabRef}
                    datas={datas?.items}
                    depotId={depotId}
                    onFinish={(tableData) => {
                        submitData.tableData = tableData;
                    }}
                    onChangeTable={(data) => {
                        setTableChangeData(data);
                    }}
                />
            </div>
            <Footer
                ref={footerRef}
                datas={datas}
                type="out"
                onFinish={(values: any) => {
                    submitData.footerData = values;
                }}
                tableData={tableChangeData}
            />
        </>
    );
});

export default DocumentDetail;
DocumentDetail.displayName = 'DocumentDetail';

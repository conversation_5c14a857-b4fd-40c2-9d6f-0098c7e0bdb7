import { ConfigGridTable, ConfigGridTableColumnsType } from '@common/components/feature';
import { UseGridTableRefAttributesReturn } from '@weway/beacon/dist/grid-table/hooks/data';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { uniqueId } from 'lodash';
import { tableSummaryTotal } from '@/utils';
type DocumentTableProps = {
    onFinish: (data: any) => void;
    datas?: any[];
    status?: number;
    id: number;
};
const DocumentTable = forwardRef((props: DocumentTableProps, ref: any) => {
    const { onFinish, datas } = props;
    const tableRef = useRef<UseGridTableRefAttributesReturn>(null);
    const [dataSource, setDataSource] = useState<any>(
        new Array(1).fill(undefined).map((_) => ({
            id: 'uuid' + uniqueId(),
        })),
    );
    const columns: Array<ConfigGridTableColumnsType<any>> = [
        {
            title: '商品编号',
            dataIndex: 'spuSn',
            key: 'spuSn',
            sortDirections: ['ascend', 'descend', 'ascend'],
            sorter(a, b) {
                return a.spuSn.localeCompare(b.spuSn);
            },
            summary: false,
        },
        {
            title: '商品名称',
            dataIndex: 'spuName',
            key: 'spuName',
            summary: false,
            sortDirections: ['ascend', 'descend', 'ascend'],
            sorter(a, b) {
                return a.spuName.localeCompare(b.spuName);
            },
        },
        {
            title: '图片',
            dataIndex: 'image',
            key: 'image',
            summary: false,
        },
        {
            title: '规格',
            dataIndex: 'specValue',
            key: 'specValue',
            summary: false,
        },
        {
            title: '条码',
            dataIndex: 'barcode',
            key: 'barcode',
            summary: false,
        },
        {
            title: '品牌',
            dataIndex: 'brandName',
            key: 'brandName',
            summary: false,
        },

        {
            title: '历史账面库存',
            dataIndex: 'beforeStock',
            key: 'beforeStock',
            summary: false,
        },
        {
            title: '数量',
            dataIndex: 'qty',
            key: 'qty',
        },
        {
            title: '单位',
            dataIndex: 'unitName',
            key: 'unitName',
            summary: false,
        },
        {
            title: '换算关系',
            dataIndex: 'unitConvertRelation',
            key: 'unitConvertRelation',
            summary: false,
        },
        {
            title: '换算结果',
            dataIndex: 'unitConvertQtyText',
            key: 'unitConvertQtyText',
            summary: false,
        },
        {
            title: '基础数量',
            dataIndex: 'basicQty',
            key: 'basicQty',
        },
        {
            title: '基础单位',
            dataIndex: 'basicUnitName',
            key: 'basicUnitName',
            summary: false,
        },
        {
            title: '成本单价',
            dataIndex: 'costPrice',
            key: 'costPrice',
            summary: false,
        },
        {
            title: '成本金额',
            dataIndex: 'costAmount',
            key: 'costAmount',
        },
        // {
        //     title: '折扣',
        //     dataIndex: 'discountRate',
        //     key: 'discountRate',
        //     summary: false,
        // },
        // {
        //     title: '折后单价',
        //     dataIndex: 'discountedPrice',
        //     key: 'discountPrice',
        //     summary: false,
        // },
        // {
        //     title: '折后金额',
        //     dataIndex: 'discountedAmount',
        //     key: 'discountAmount',
        // },
        {
            title: '明细备注',
            dataIndex: 'remark',
            key: 'remark',

            summary: false,
        },
    ];
    useEffect(() => {
        if (datas && datas.length > 0) {
            // const length = datas.length;
            // let list: any = [];
            // if (length < 10) {
            //     list = new Array(10 - length).fill(undefined).map((_) => ({
            //         id: 'uuid' + uniqueId(),
            //     }));
            // }
            setDataSource([
                ...datas.map((item: any) => {
                    return {
                        ...item,
                    };
                }),
                // ...list,
            ]);
        } else {
            setDataSource(
                new Array(1).fill(undefined).map((_) => ({
                    id: 'uuid' + uniqueId(),
                })),
            );
        }
    }, [datas]);
    useImperativeHandle(ref, () => ({
        submit() {
            setDataSource(tableRef.current.getDataSource());
            onFinish(tableRef.current.getDataSource());
        },
    }));
    return (
        <>
            <ConfigGridTable
                ref={tableRef}
                configId=""
                resizable
                columns={columns}
                rowKey={'id'}
                readonly={true}
                align="center"
                dataSource={dataSource}
                editableIcon={false}
                scroll={{
                    y: 500,
                }}
                summaryTotal={tableSummaryTotal}
            />
        </>
    );
});

export default DocumentTable;
DocumentTable.displayName = 'DocumentTable';

import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { DocumentDetailProps } from './data';
import { GridForm } from '@weway/beacon';
import { FormatTextAuto, Text } from '@common/components';
import Footer from '../footer';
import DocumentTable from './document-detail';
import dayjs from 'dayjs';
import { ReadOnlyDataCard } from '@/components/feature/read-only-data-card';
/**
 * 单据详情
 * @returns
 */

const DocumentDetail = forwardRef((props: DocumentDetailProps, ref) => {
    const { datas } = props;

    const tabRef = useRef<any>();
    const footerRef = useRef<any>();
    const [form] = GridForm.useForm();
    const submitData: any = {
        formData: {},
        tableData: [],
        footerData: {},
    };

    useEffect(() => {
        console.log('详情', datas);
        if (datas?.id) {
            datas.businessDate = dayjs.unix(datas?.businessDate);
            form.setFieldsValue(datas);
        }
    }, [datas]);

    useImperativeHandle(ref, () => ({
        async submit() {
            form.submit();
        },
    }));

    return (
        <>
            <ReadOnlyDataCard
                dataSource={{
                    businessNumber: datas?.businessNumber,
                    businessDate: dayjs.unix(datas?.businessDate).format('YYYY-MM-DD HH:mm:ss'),
                    traderName: datas?.traderName,
                    handlerName: datas?.handlerName,
                    departmentName: datas?.departmentName,
                    brief: datas?.brief,
                }}
                columns={[
                    {
                        title: <FormatTextAuto.Locales id="单据编号" />,
                        key: 'businessNumber',
                        render: (option) => {
                            const { value } = option;
                            return <Text type="primary">{value}</Text>;
                        },
                    },

                    {
                        title: <FormatTextAuto.Locales id="单据日期" />,
                        key: 'businessDate',
                    },
                    {
                        title: <FormatTextAuto.Locales id="往来单位" />,
                        key: 'traderName',
                    },
                    {
                        title: <FormatTextAuto.Locales id="经手人" />,
                        key: 'handlerName',
                    },
                    {
                        title: <FormatTextAuto.Locales id="部门" />,
                        key: 'departmentName',
                    },

                    {
                        title: <FormatTextAuto.Locales id="单据说明" />,
                        key: 'brief',
                    },
                ]}
            />

            <div style={{ marginTop: 10, marginBottom: 10 }}>
                <DocumentTable
                    ref={tabRef}
                    datas={datas?.items}
                    status={datas?.status}
                    onFinish={(tableData) => {
                        submitData.tableData = tableData;
                    }}
                />
            </div>
            <Footer
                ref={footerRef}
                datas={datas}
                onFinish={(values: any) => {
                    submitData.footerData = values;
                }}
            />
        </>
    );
});
DocumentDetail.displayName = 'DocumentDetail';
export default DocumentDetail;

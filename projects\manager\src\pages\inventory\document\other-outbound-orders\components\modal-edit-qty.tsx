import { FormatTextAuto, GridFormCollapse, Modal, ModalProps } from '@common/components';
import { Form, GridInputNumber, Spin } from '@weway/beacon';
import React from 'react';
import { useIntl } from '@weway/i18n';
import { useBoolean } from 'ahooks';
import { GRID_FORM_MODAL_WIDTH } from '@/common';

/**
 * 修改整单数量
 * @returns
 */
type ModalEditQtyProps = {
    dataSource?: any;
    onOk: (params: any) => void;
    onCancel: () => void;
} & Pick<ModalProps, 'open'>;
const ModalEditQty = (props: ModalEditQtyProps) => {
    const { open, onCancel, onOk } = props;

    const [form] = Form.useForm();

    const intl = useIntl();

    const [loading, { setTrue: setLoadingTrue, setFalse: setLoadingFalse }] = useBoolean(false);
    const formConfig = GridFormCollapse.getLayoutConfig({
        type: 'modal-small',
        columnCount: 3,
    });
    const { width } = Modal.getLayoutConfig({ columnCount: 3, maxColumns: formConfig.column });
    const _onFinish = async (values: any) => {
        if (!values) return;
        setLoadingTrue();
        onOk?.(values);
        setLoadingFalse();
    };

    const handleCancel = () => {
        onCancel?.();
    };

    return (
        <Modal
            open={open}
            title={<FormatTextAuto.Locales id={'批量设置数量'} />}
            width={width}
            onOk={form.submit}
            onCancel={handleCancel}
            destroyOnClose
            afterClose={form.resetFields}
        >
            <Spin spinning={loading}>
                <GridFormCollapse
                    form={form}
                    onFinish={_onFinish}
                    {...formConfig}
                    defaultCollapsed={false}
                    renderSubmitter={() => null}
                    expandIcon={() => null}
                >
                    <GridFormCollapse.Item
                        label={<FormatTextAuto.Locales id={'商品数量'} />}
                        name="qty"
                        rules={[{ required: true, message: intl.formatMessage({ id: '请输入商品数量' }) }]}
                    >
                        <GridInputNumber style={{ width: GRID_FORM_MODAL_WIDTH }} />
                    </GridFormCollapse.Item>
                </GridFormCollapse>
            </Spin>
        </Modal>
    );
};
ModalEditQty.displayName = 'ModalEditQty';
export default ModalEditQty;

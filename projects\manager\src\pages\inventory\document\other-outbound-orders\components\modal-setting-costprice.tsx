import { LayoutContent } from '@common/components/feature';
import { Button, FormatTextAuto, GridInput, Modal, Space } from '@common/components';
import { Form, GridTable, GridTableColumnsType, GridTableComponentRefAttributesType, message } from '@weway/beacon';
import React, { FC, useRef, useState } from 'react';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useRequest } from 'ahooks';
import { postOtherAddOrSubmit, postOtherEditOrSubmit } from '@common/services/api/admin/mods/inventory';
import { useTags } from '@weway/ui';

type ModalSettingCostPriceProps = {
    dataSource: any;
    formData: any; //单据数据
    open: boolean;
    onClose: () => void;
    onOk: () => void;
};
const ModalSettingCostPrice: FC<ModalSettingCostPriceProps> = (props) => {
    const { open, dataSource, formData, onClose, onOk } = props;
    const [form] = Form.useForm();
    const intl = useIntl();
    const { navigate } = useTags();
    const tableRef = useRef<GridTableComponentRefAttributesType>(null);
    const columns: Array<GridTableColumnsType> = [
        {
            title: <FormattedMessage id={'商品编号'} />,
            dataIndex: 'sn',
            key: 'sn',
            align: 'center',
        },
        {
            title: <FormattedMessage id={'商品名称'} />,
            dataIndex: 'spuName',
            key: 'spuName',
            align: 'center',
        },
        {
            title: <FormattedMessage id={'规格'} />,
            dataIndex: 'specValue',
            key: 'specValue',
            align: 'center',
        },

        {
            title: <FormattedMessage id={'成本单价'} />,
            dataIndex: 'costPrice',
            key: 'costPrice',
            align: 'center',
            readonly: false,
            type: 'input-number',
        },
        {
            title: <FormattedMessage id={'单位'} />,
            dataIndex: 'unitName',
            key: 'unitName',
            align: 'center',
        },
    ];
    const [batchPrice, setBatchPrice] = useState(null);
    //修改
    const { loading: loadingUpdate, runAsync: runAsyncUpdate } = useRequest(postOtherEditOrSubmit, { manual: true });
    //新增
    const { loading, runAsync } = useRequest(postOtherAddOrSubmit, { manual: true });
    const _onOK = async () => {
        const selectedRows = tableRef.current?.getDataSource() || [];

        //校验金额
        const isPrice = selectedRows.every((item: any) => item.costPrice);
        if (!isPrice) {
            message.error(intl.formatMessage({ id: '请输入成本单价' }));
            return;
        }
        formData.items.forEach((item: any) => {
            selectedRows.forEach((row: any) => {
                if (item.skuId === row.id) {
                    item.costPrice = row.costPrice;
                }
            });
        });

        if (formData.mode === 'edit') {
            //修改保存
            try {
                await runAsyncUpdate(formData, intl);
                onOk();
                message.success(intl.formatMessage({ id: '提交成功' }));
                if (formData.billStatus === 100) {
                    navigate('/inventory/document/other-outbound-orders');
                }
            } catch (error) {}
        } else {
            try {
                await runAsync(formData, intl);
                onOk();
                message.success(intl.formatMessage({ id: '提交成功' }));
                if (formData.billStatus === 100) {
                    navigate('/inventory/document/other-outbound-orders');
                }
            } catch (error) {}
        }
    };

    return (
        <Modal
            title={<FormatTextAuto.Locales id={'输入成本价'} />}
            width={840}
            open={open}
            onOk={_onOK}
            onCancel={onClose}
            destroyOnClose
            okButtonProps={{ loading: loading || loadingUpdate }}
            afterClose={() => {
                //关闭弹窗时清空表单
                form.resetFields();
                //清空批量设置价格输入框
                setBatchPrice(null);
            }}
        >
            <Space direction="vertical">
                <Space>
                    <GridInput
                        value={batchPrice}
                        placeholder={intl.formatMessage({ id: '成本价' })}
                        onChange={(e) => {
                            setBatchPrice(e.target.value);
                        }}
                    />
                    <Button
                        type="primary"
                        onClick={() => {
                            const selectedRows = tableRef.current?.getSelectItems() || [];
                            if (selectedRows.length === 0) {
                                message.error(intl.formatMessage({ id: '请选择商品' }));
                                return;
                            }

                            selectedRows.forEach((item: any, index: number) => {
                                item.costPrice = batchPrice;
                                tableRef.current?.update(index, item);
                            });
                        }}
                    >
                        <FormattedMessage id={'批量设置'} />
                    </Button>
                </Space>
                <LayoutContent>
                    <GridTable
                        ref={tableRef}
                        rowKey={'id'}
                        dataSource={dataSource}
                        columns={columns}
                        readonly={true}
                        resizable={true}
                        rowSelection={true}
                    />
                </LayoutContent>
            </Space>
        </Modal>
    );
};
export default ModalSettingCostPrice;

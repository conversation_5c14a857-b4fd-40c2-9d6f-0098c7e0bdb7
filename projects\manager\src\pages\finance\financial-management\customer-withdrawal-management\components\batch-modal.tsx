import { useBankAccount } from '@/hooks/useBankAccount';
import {
    FormatTextAuto,
    GridFormCollapse,
    GridSelect,
    Modal,
    ModalProps,
    ModalRefAttributes,
} from '@common/components';
import { postCashoutAudit, postCashoutEnsure } from '@common/services/api/admin/mods/customer';
import { Form, GridFormProps, GridInput, GridRadio } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useRequest } from 'ahooks';
import React, { FC, forwardRef, ForwardRefExoticComponent, RefAttributes, useEffect } from 'react';

export const WAY_KEYMAP: Record<number, string> = {
    0: '其他',
    1: '银行卡',
    2: '微信',
    3: '支付宝',
};

type ModalBaseProps = {
    onSubmitSuccess?: () => void;
    formData?: any;
    status?: any; //规格组列表
    item?: any; //
} & Pick<ModalProps, 'afterClose' | 'title' | 'okButtonProps' | 'text' | 'open' | 'onCancel' | 'footer'> &
    Pick<GridFormProps, 'onFinish' | 'onFinishFailed' | 'form' | 'disabled'> & {
        onCancel?: (e?: React.MouseEvent<HTMLElement>) => void;
    };

const ModalBase: ForwardRefExoticComponent<RefAttributes<ModalRefAttributes> & ModalBaseProps> = forwardRef(
    (props, ref) => {
        const { open, title, form, afterClose, footer, onCancel, onFinish, onFinishFailed, status } = props;

        const { selectOptions: selectOptionsBankAccountData } = useBankAccount();

        const formConfig = GridFormCollapse.getLayoutConfig({
            type: 'modal-small',
            columnCount: 4,
        });
        //提交表单验证成功回调
        const _onFinish: GridFormProps['onFinish'] = (value) => {
            onFinish({ ...value });
        };
        //提交表单验证失败回调
        const _onFinishFailed: GridFormProps['onFinishFailed'] = (value) => {
            onFinishFailed(value);
        };

        // const { width } = Modal.getLayoutConfig({ columnCount: 4, maxColumns: formConfig.column });
        useEffect(() => {
            console.log(status, 'status');
        }, [status]);

        return (
            <Modal
                title={title}
                width={580}
                open={open}
                onOk={form.submit}
                ref={ref}
                afterClose={afterClose}
                footer={footer}
                onCancel={onCancel}
            >
                <GridFormCollapse
                    id={'form'}
                    {...formConfig}
                    form={form}
                    onFinish={_onFinish}
                    onFinishFailed={_onFinishFailed}
                    defaultCollapsed={false}
                    renderSubmitter={() => null}
                    expandIcon={() => null}
                    // disabled={disabled}
                    // initialValues={{ rechargeQuota: 0 }}
                    // labelCol={{ span: 4 }}
                    // wrapperCol={{ span: 10 }}
                >
                    {status !== 9 && (
                        <GridFormCollapse.Item
                            label={<FormatTextAuto.Locales id={'付款账户'} />}
                            name="payAccountId"
                            required
                        >
                            <GridSelect options={selectOptionsBankAccountData} />
                        </GridFormCollapse.Item>
                    )}
                    {status !== 9 && (
                        <GridFormCollapse.Item
                            label={<FormatTextAuto.Locales id={'打印凭证'} />}
                            name="paymentVoucherUrl"
                        >
                            <>(to do)</>
                        </GridFormCollapse.Item>
                    )}
                    {status === 9 && (
                        <GridFormCollapse.Item
                            label={<FormatTextAuto.Locales id={'审核通过'} />}
                            name="status"
                            required
                        >
                            <GridRadio.Group
                                options={[
                                    {
                                        label: '审核通过',
                                        value: 3,
                                    },
                                    {
                                        label: '拒绝',
                                        value: 2,
                                    },
                                ]}
                                onChange={(e) => {
                                    console.log(e);
                                }}
                            />
                        </GridFormCollapse.Item>
                    )}
                    <GridFormCollapse.Item
                        label={<FormatTextAuto.Locales id={'备注'} />}
                        name="remark"
                        disabled={status === 100 ? true : false}
                    >
                        <GridInput.TextArea rows={4} />
                    </GridFormCollapse.Item>
                </GridFormCollapse>
            </Modal>
        );
    },
);
ModalBase.displayName = 'ModalBase';

type ModalBatchAuditProps = {
    onSubmitSuccess?: () => void;
    item: any;
} & Pick<ModalBaseProps, 'open' | 'onCancel'>;
export const ModalBatchAudit: FC<ModalBatchAuditProps> = (props) => {
    const { onCancel, open, item, onSubmitSuccess } = props;
    const [form] = Form.useForm();
    const intl = useIntl();
    const { runAsync: runAsyncAudit } = useRequest(postCashoutAudit, {
        manual: true,
    });

    const reset = () => {
        form.resetFields();
    };

    const onFinish = async (value: any) => {
        try {
            const params = {
                ids: item,
                status: value.status,
                reason: value.remark,
            };
            // console.log(params, '---params---');
            await runAsyncAudit(params, intl);
            onSubmitSuccess();
        } catch (error) {}
    };
    const onFinishFailed = () => {};
    return (
        <ModalBase
            title={<FormattedMessage id={'批量审核'} />}
            open={open}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            form={form}
            afterClose={reset}
            onCancel={onCancel}
            // item={data}
            status={9}
            // footer={footer}
        />
    );
};

ModalBatchAudit.displayName = 'ModalBatchAudit';

type ModalBatchWithdrawProps = {
    onSubmitSuccess?: () => void;
    item: any;
} & Pick<ModalBaseProps, 'open' | 'onCancel'>;
export const ModalBatchWithdraw: FC<ModalBatchWithdrawProps> = (props) => {
    const { onCancel, open, item, onSubmitSuccess } = props;
    const [form] = Form.useForm();
    const intl = useIntl();

    const { runAsync: runCashoutEnsure } = useRequest(postCashoutEnsure, {
        manual: true,
    });

    const reset = () => {
        form.resetFields();
    };

    const onFinish = async (value: any) => {
        try {
            const params = {
                ids: item,
                payAccountId: value.payAccountName,
                paymentVoucherUrl: 'todo',
                confirmedReason: value.remark,
                status: value.status,
            };
            await runCashoutEnsure(params, intl);
            onSubmitSuccess();
        } catch (error) {}
    };

    const onFinishFailed = () => {};
    // const intl = useIntl();
    return (
        <ModalBase
            title={<FormattedMessage id={'批量提现'} />}
            open={open}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            form={form}
            afterClose={reset}
            onCancel={onCancel}
            // item={data}
            status={70}
            // footer={
            //     <div>
            //         <Button type="primary" onClick={form.submit}>
            //             <FormattedMessage id={'确认提现'} />
            //         </Button>
            //         <Button onClick={onRejectFinish} style={{ marginLeft: 40 }}>
            //             <FormattedMessage id={'返回审核'} />
            //         </Button>
            //         <Button onClick={(e) => onCancel(e)} style={{ marginLeft: 40 }}>
            //             <FormattedMessage id={'取消'} />
            //         </Button>
            //     </div>
            // }
        />
    );
};

ModalBatchWithdraw.displayName = 'ModalBatchWithdraw';

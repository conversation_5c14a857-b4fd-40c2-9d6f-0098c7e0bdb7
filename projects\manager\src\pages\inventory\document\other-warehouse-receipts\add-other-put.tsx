import React, { FC, useEffect, useRef, useState } from 'react';
import { Button, Space } from '@common/components';
import { AuthComponent } from '@common/components/feature';
import { message, Spin, Tabs } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import dayjs from 'dayjs';
import { useKeyPress, useRequest } from 'ahooks';
import { getOtherGet, postOtherAddOrSubmit, postOtherEditOrSubmit } from '@common/services/api/admin/mods/inventory';
import { useLocation } from 'react-router-dom';
import DocumentDetail from './components';
import { postLatestcodeGet } from '@common/services/api/admin/mods/basic';
import { set } from 'lodash';

/**
 * 其他入库单
 * @returns
 */
const AddOtherPut: FC = () => {
    const portalTarget = useRef<HTMLDivElement>(null);
    const ref = useRef<any>();
    const intl = useIntl();
    const { state } = useLocation(); //接收参数
    const onChange = (key: string) => {
        console.log(key);
    };
    const billStatus = useRef(0);
    const [mData, setMData] = useState<any>();
    const { loading, runAsync } = useRequest(postOtherAddOrSubmit, { manual: true });
    useKeyPress('alt.s', () => {
        billStatus.current = 100;
        ref.current.submit();
    });
    useKeyPress('alt.c', () => {
        billStatus.current = 0;
        ref.current.submit();
    });
    useKeyPress('alt.q', () => {
        handleClear();
    });
    useKeyPress('alt.n', () => {
        handlerAdd();
    });
    //获取详情
    const { loading: loadingGet, runAsync: runAsyncGet } = useRequest(getOtherGet, { manual: true });
    //修改
    const { loading: loadingUpdate, runAsync: runAsyncUpdate } = useRequest(postOtherEditOrSubmit, { manual: true });
    //生成单据编号
    const { runAsync: runAsyncGetCode } = useRequest(postLatestcodeGet, { manual: true });
    const initCode = async () => {
        try {
            const { value } = await runAsyncGetCode({ type: 303 }, intl);
            setMData({ ...mData, businessNumber: value });
        } catch (error) {}
    };
    const handleInit = async () => {
        try {
            const data = await runAsyncGet(state?.id, state?.isCopy, intl);
            set(data, 'businessDate', dayjs.unix(data.businessDate as any));
            data.departmentId = data?.departmentId || null;
            data.deliveryMode = data?.deliveryMode || null;

            if (state?.isCopy) {
                //复制单据时，单据日期默认为当天
                set(data, 'businessDate', dayjs());
                set(data, 'status', 0);
                set(data, 'id', null);
                try {
                    const { value } = await runAsyncGetCode({ type: 303 }, intl);
                    set(data, 'businessNumber', value);
                } catch (error) {}
            }
            setMData(data);
        } catch (error) {}
    };
    useEffect(() => {
        if (state?.id) {
            handleInit();
        } else {
            initCode();
        }
    }, [state?.id]);

    //清空
    const handleClear = () => {
        setMData({
            ...mData,
            items: [],
        });
    };
    const handlerAdd = async () => {
        try {
            const { value } = await runAsyncGetCode({ type: 303 });
            setMData({
                id: null,
                businessNumber: value,
                businessDate: dayjs(),
                traderName: '',
                departmentId: null,
                depotId: null,
                traderId: null,
                deliveryMode: null,
                deliveryName: '',
                deliveryPhone: '',
                PackQty: '',
                handlerId: null,
                status: 0,
                brief: '',
                remark: '',
                items: [],
            });
        } catch (error) {}
    };
    return (
        <Spin spinning={loading || loadingGet || loadingUpdate}>
            <Tabs
                defaultActiveKey="1"
                items={[
                    {
                        key: '1',
                        label: <FormattedMessage id={'其他入库单'} />,
                        children: (
                            <DocumentDetail
                                getConfigSettingContainer={() => portalTarget}
                                ref={ref}
                                datas={mData}
                                isCopy={state?.isCopy}
                                onFinish={async (value: any) => {
                                    value.formData.businessDate = dayjs(value.formData.businessDate).unix();
                                    value.formData.planTime = dayjs(value.formData.planTime).unix();
                                    const newData = {
                                        ...value.formData,
                                        remark: value.footerData.remarks,
                                        type: 303,
                                        items: value.tableData
                                            .filter((its: any) => its?.skuId)
                                            .map((item: any) => {
                                                return {
                                                    skuId: item.skuId,
                                                    //如果是复制，则id为空
                                                    id:
                                                        String(item?.id).indexOf('uuid') === -1 &&
                                                        String(item?.id).indexOf('-') === -1 &&
                                                        !state?.isCopy
                                                            ? item.id
                                                            : null,
                                                    unitType: item.unitType,
                                                    qty: item.qty,
                                                    basicQty: item.basicQty,
                                                    price: item.price,
                                                    amount: item.amount,
                                                    discountRate: item.discountRate,
                                                    discountPrice: item.discountedPrice,
                                                    discountAmount: item.discountedAmount,
                                                    remark: item.remark || '',
                                                };
                                            }),
                                    };
                                    if (newData.items.length === 0) {
                                        message.error(intl.formatMessage({ id: '请选择商品' }));
                                        return;
                                    }
                                    if (billStatus.current === 100) {
                                        //提交单据
                                        newData.billStatus = 100;
                                    } else if (billStatus.current === 0) {
                                        //存为草稿
                                        newData.billStatus = 0;
                                    }

                                    console.log('newData', newData.items);
                                    //校验表格
                                    // const isQty = newData.items.every(
                                    //     (item: any) =>
                                    //         item.qty &&
                                    //         item.basicQty &&
                                    //         item.price &&
                                    //         item.amount &&
                                    //         item.discountRate &&
                                    //         item.discountPrice &&
                                    //         item.discountAmount,
                                    // );
                                    // if (!isQty && billStatus.current === 100) {
                                    //     message.error(intl.formatMessage({ id: '请完善商品信息' }));
                                    //     return;
                                    // }
                                    delete newData.businessNumber;
                                    if (mData?.id && !state?.isCopy) {
                                        //修改保存
                                        try {
                                            await runAsyncUpdate({ ...newData, id: mData?.id }, intl);
                                            if (newData.billStatus == 0)
                                                message.success(intl.formatMessage({ id: '保存成功' }));
                                            else message.success(intl.formatMessage({ id: '提交成功' }));
                                            handlerAdd();
                                        } catch (error) {}
                                    } else {
                                        //新增
                                        try {
                                            await runAsync(newData, intl);
                                            if (newData.billStatus == 0)
                                                message.success(intl.formatMessage({ id: '保存成功' }));
                                            else message.success(intl.formatMessage({ id: '提交成功' }));
                                            handlerAdd();
                                        } catch (error) {
                                            console.log(error);
                                        }
                                    }
                                }}
                            />
                        ),
                    },
                ]}
                onChange={onChange}
                tabBarExtraContent={
                    <>
                        <Space>
                            <div ref={portalTarget}></div>
                            <AuthComponent id="17010201-4">
                                <Button
                                    type="secondary"
                                    onClick={() => {
                                        handlerAdd();
                                    }}
                                >
                                    <FormattedMessage id={'新增(N)'} />
                                </Button>
                            </AuthComponent>
                            <AuthComponent id="17010201-128">
                                <Button onClick={() => {}}>
                                    <FormattedMessage id={'打印(P)'} />
                                </Button>
                            </AuthComponent>
                            {(mData?.status === 0 || !state?.id || state?.isCopy) && (
                                <AuthComponent id="">
                                    <Button
                                        onClick={() => {
                                            handleClear();
                                        }}
                                    >
                                        <FormattedMessage id={'清空商品(Q)'} />
                                    </Button>
                                </AuthComponent>
                            )}
                            {(mData?.status === 0 || !state?.id || state?.isCopy) && (
                                <AuthComponent id="">
                                    <Button>
                                        <FormattedMessage id={'导入商品(D)'} />
                                    </Button>
                                </AuthComponent>
                            )}
                            {(mData?.status === 0 || !state?.id || state?.isCopy) && (
                                <AuthComponent id="17010201-16">
                                    <Button
                                        onClick={() => {
                                            billStatus.current = 0;
                                            ref.current.submit();
                                        }}
                                    >
                                        <FormattedMessage id={'存为草稿(C)'} />
                                    </Button>
                                </AuthComponent>
                            )}
                            {(mData?.status === 0 || !state?.id || state?.isCopy) && (
                                <AuthComponent id="17010201-32">
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            billStatus.current = 100;
                                            ref.current.submit();
                                        }}
                                    >
                                        <FormattedMessage id={'提交单据(S)'} />
                                    </Button>
                                </AuthComponent>
                            )}
                        </Space>
                    </>
                }
            />
        </Spin>
    );
};

AddOtherPut.displayName = 'AddOtherPut';

export default AddOtherPut;

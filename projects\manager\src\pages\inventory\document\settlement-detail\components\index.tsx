import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { DocumentDetailProps } from './data';
import { GridForm } from '@weway/beacon';
import { FormatTextAuto, Space, Tag, Text } from '@common/components';
import Footer from './footer';
import DocumentTable from './DocumentTable';
import dayjs from 'dayjs';
import { DELIVERY_MODE, TRANSFER_TYPE } from '@/common/message';
import { ReadOnlyDataCard } from '@/components/feature/read-only-data-card';
/**
 * 单据详情
 * @returns
 */
const DocumentDetail = forwardRef((props: DocumentDetailProps, ref: any) => {
    const { datas } = props;

    const tabRef = useRef<any>();
    const footerRef = useRef<any>();
    const [form] = GridForm.useForm();
    const submitData: any = {
        formData: {},
        tableData: [],
        footerData: {},
    };

    useEffect(() => {
        console.log('详情', datas);
        if (datas?.id) {
            form.setFieldsValue(datas);
        } else {
            form.resetFields();
            form.setFieldValue('businessNumber', datas?.businessNumber); // 单据编号
        }
    }, [datas]);

    useImperativeHandle(ref, () => ({
        async submit() {
            form.submit();
        },
    }));
    return (
        <>
            <ReadOnlyDataCard
                dataSource={{
                    businessNumber: datas?.businessNumber,
                    businessDate: dayjs.unix(datas?.businessDate).format('YYYY-MM-DD HH:mm:ss'),
                    transferType: TRANSFER_TYPE[datas?.transferType],
                    handlerName: datas?.handlerName,
                    departmentName: datas?.departmentName,
                    fromDepotName: datas?.fromDepotName,
                    toDepotName: datas?.toDepotName,
                    deliveryMode: DELIVERY_MODE[datas?.deliveryMode],
                    deliveryName: datas?.deliveryName,
                    deliveryPhone: datas?.deliveryPhone,
                    isChangePrice: datas?.isChangePrice ? '是' : '否',

                    brief: datas?.brief,
                    packQty: datas?.packQty,
                    originalNumber: datas?.originalNumber,
                }}
                columns={[
                    {
                        title: <FormatTextAuto.Locales id="单据编号" />,
                        key: 'businessNumber',
                        render: (option) => {
                            const { value } = option;
                            return (
                                <Space>
                                    <Text type="primary">{value}</Text>
                                    <Tag type={'Done'} />
                                </Space>
                            );
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="单据日期" />,
                        key: 'businessDate',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="调拨类型" />,
                        key: 'transferType',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="经手人" />,
                        key: 'handlerName',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="部门" />,
                        key: 'departmentName',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="调出仓库" />,
                        key: 'fromDepotName',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="调入仓库" />,
                        key: 'toDepotName',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },

                    {
                        title: <FormatTextAuto.Locales id="承运方式" />,
                        key: 'deliveryMode',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="承运单位" />,
                        key: 'deliveryName',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="承运电话" />,
                        key: 'deliveryPhone',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="变价调拨" />,
                        key: 'isChangePrice',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="单据说明" />,
                        key: 'brief',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="打包件数" />,
                        key: 'packQty',
                        render: (option) => {
                            const { value } = option;
                            return <Text>{value}</Text>;
                        },
                    },
                    {
                        title: <FormatTextAuto.Locales id="原申请单" />,
                        key: 'originalNumber',
                        render: (option) => {
                            const { value } = option;
                            return <Text type="link">{value}</Text>;
                        },
                    },
                ]}
            />
            <div style={{ marginTop: 10, marginBottom: 10 }}>
                <DocumentTable
                    ref={tabRef}
                    datas={datas?.items}
                    status={datas?.status}
                    id={datas?.id}
                    onFinish={(tableData) => {
                        submitData.tableData = tableData;
                    }}
                />
            </div>
            <Footer
                ref={footerRef}
                datas={datas}
                onFinish={(values: any) => {
                    submitData.footerData = values;
                }}
            />
        </>
    );
});

export default DocumentDetail;
DocumentDetail.displayName = 'DocumentDetail';

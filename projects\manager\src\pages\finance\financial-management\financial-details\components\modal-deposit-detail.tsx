import { Button, FormattedMessageSpan, FormatTextAuto, Modal, Tag, Text } from '@common/components';
import { Input, Space, Spin } from '@weway/beacon';
import React, { useEffect, useRef } from 'react';
import { ReadOnlyDataCard } from '@/components/feature/read-only-data-card';
import { ConfigGridTable } from '@common/components/feature';
import { UseGridTableRefAttributesReturn } from '@weway/beacon/dist/grid-table/hooks/data';
import { get as _get } from 'lodash';
import { useIntl } from '@weway/i18n';
import { useRequest } from 'ahooks';
import { getCashoutGet } from '@common/services/api/admin/mods/customer';
import dayjs from 'dayjs';
import { CASHOUT_WAY } from '@/common/message';
/**
 * 提现单详情
 * @returns
 */
type ModalDepositDetailProps = {
    open: boolean;
    id: number;
    onClose: () => void;
};
const ModalDepositDetail = (props: ModalDepositDetailProps) => {
    const { open, onClose, id } = props;

    const intl = useIntl();

    const tableRef = useRef<UseGridTableRefAttributesReturn>(null);

    const handleOk = async () => {};

    const handleCancel = () => {};
    const { runAsync, data } = useRequest(getCashoutGet, {
        manual: true,
    });
    const init = async () => {
        try {
            await runAsync(id, intl);
        } catch (error) {}
    };
    useEffect(() => {
        if (open) {
            init();
        }
    }, [open]);
    return (
        <Modal
            title={<FormattedMessageSpan id="提现单详情" />}
            open={open}
            width={'1180'}
            onOk={handleOk}
            onCancel={handleCancel}
            footer={
                <Space>
                    <Button onClick={onClose}>
                        <FormattedMessageSpan id="取消(Esc)" />
                    </Button>
                </Space>
            }
            destroyOnClose
        >
            <Spin spinning={false}>
                <ReadOnlyDataCard
                    style={{ marginBottom: 10 }}
                    dataSource={{
                        businessNumber: data?.businessNumber,
                        appliedTime: dayjs.unix(data?.appliedTime as any).format('YYYY-MM-DD HH:mm:ss'),
                        customerUserName: data?.customerUserName,
                        type: data?.type === 0 ? '预存款提现' : '分销佣金提现',
                        amount: data?.amount,
                        contactName: data?.contactName,
                        contactPhone: data?.contactPhone,
                        way: CASHOUT_WAY[data?.way],
                        accountHolder: data?.accountHolder,
                        accountNumber: data?.accountNumber,
                        bankName: data?.bankName,

                        remark: data?.remark,
                    }}
                    columns={[
                        {
                            title: <FormatTextAuto.Locales id="单据编号" />,
                            key: 'businessNumber',
                            span: 8,
                            render: (option) => {
                                return (
                                    <Space>
                                        <Text type="primary">{option.value}</Text>
                                        <Tag type="Done" />
                                    </Space>
                                );
                            },
                        },
                        {
                            title: <FormatTextAuto.Locales id="申请时间" />,
                            key: 'appliedTime',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="客户名称" />,
                            key: 'customerUserName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="提现类型" />,
                            key: 'type',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="提现金额" />,
                            key: 'amount',
                            span: 8,
                            render: (option) => {
                                return <Text type="danger">{option.value}</Text>;
                            },
                        },
                        {
                            title: <FormatTextAuto.Locales id="联系人" />,
                            key: 'contactName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="联系电话" />,
                            key: 'contactPhone',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="提现方式" />,
                            key: 'way',
                            span: 8,
                            render: (option) => {
                                return <Text type="danger">{option.value}</Text>;
                            },
                        },
                        {
                            title: <FormatTextAuto.Locales id="姓名" />,
                            key: 'accountHolder',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="账号" />,
                            key: 'accountNumber',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="开户银行" />,
                            key: 'bankName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="收款二维码" />,
                            key: 'departmentName',
                            span: 8,
                        },
                    ]}
                />

                <ConfigGridTable
                    ref={tableRef}
                    configId=""
                    resizable
                    columns={[
                        {
                            title: '操作',
                            dataIndex: 'options',
                            key: 'options',
                            align: 'center',
                        },
                        {
                            title: '操作时间',
                            dataIndex: 'creationTime',
                            key: 'creationTime',
                            align: 'center',
                        },
                        {
                            title: '操作人',
                            dataIndex: 'createdName',
                            key: 'createdName',
                            align: 'center',
                        },
                        {
                            title: '操作ip',
                            dataIndex: 'ipAddress',
                            key: 'ipAddress',
                            align: 'center',
                        },
                        {
                            title: '操作类型',
                            dataIndex: 'operationType',
                            key: 'operationType',
                            align: 'center',
                        },
                        {
                            title: '操作日志',
                            dataIndex: 'content',
                            key: 'content',
                            align: 'center',
                        },
                    ]}
                    rowKey={'code'}
                    dataSource={data?.logs || []}
                    editableIcon={false}
                    readonly={true}
                    summaryTotal={(dataSource, dataIndex) => {
                        return {
                            value: dataIndex
                                ? dataSource.reduce((pre, cur) => pre + (Number(_get(cur, dataIndex)) || 0), 0)
                                : '',
                        };
                    }}
                    scroll={{ y: 300 }}
                />
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span>
                        <FormattedMessageSpan id="追加日志" />：
                    </span>
                    <Input.TextArea style={{ width: 400, marginRight: 10 }} />
                    <Button type="primary">
                        <FormattedMessageSpan id="确认追加" />
                    </Button>
                </div>
            </Spin>
        </Modal>
    );
};
ModalDepositDetail.displayName = 'ModalDepositDetail';
export default ModalDepositDetail;

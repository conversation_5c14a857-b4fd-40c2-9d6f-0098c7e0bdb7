import { DEBOUNCE_TIME, GRID_FORM_MODAL_WIDTH } from '@/common';
import { LayoutHeader } from '@common/components/feature';
import { formatSelectData } from '@/utils';
import { FormatTextAuto, Modal, ModalRefAttributes, GridInput, Button } from '@common/components';
import {
    getDepotSlotGet,
    getDepotSlotGetSortIndex,
    postDepotList,
    postDepotSlotAdd,
    postDepotSlotEdit,
} from '@common/services/api/admin/mods/inventory';
import {
    Form,
    message,
    GridFormProps,
    GirdFormInstance,
    ModalProps,
    GridSwitch,
    GridSelect,
    GridInputNumber,
} from '@weway/beacon';
import { useIntl } from '@weway/i18n';
import { useRequest } from 'ahooks';
import React, {
    FC,
    forwardRef,
    ForwardRefExoticComponent,
    ReactNode,
    RefAttributes,
    useEffect,
    useRef,
    useState,
} from 'react';
type ModalBaseProps = {
    // text: ReactNode;
    formData?: any;
    title: ReactNode;
    onFinish: GridFormProps['onFinish'];
    onFinishFailed: GridFormProps['onFinishFailed'];
    form: GirdFormInstance<any>;
    afterClose?: ModalProps['afterClose'];
    okButtonProps?: ModalProps['okButtonProps'];
    disabled?: boolean;
    footer?: ReactNode;
    groupList?: any; //规格组列表
    open?: boolean; // 新增属性
    onCancel?: () => void; // 新增属性
    groupMode?: number;
};

const ModalBase: ForwardRefExoticComponent<RefAttributes<ModalRefAttributes> & ModalBaseProps> = forwardRef(
    (props, ref) => {
        const { title, onFinish, onFinishFailed, form, afterClose, disabled, footer, groupList, open, onCancel } =
            props;
        //提交表单验证成功回调
        const _onFinish: GridFormProps['onFinish'] = (value) => {
            onFinish(value);
        };
        //提交表单验证失败回调
        const _onFinishFailed: GridFormProps['onFinishFailed'] = (value) => {
            onFinishFailed(value);
        };

        const formConfig = LayoutHeader.GridFormCollapse.getLayoutConfig({
            type: 'modal-small',
            columnCount: 4,
        });
        const intl = useIntl();
        const holderName = intl.formatMessage({ id: '请输入仓库值' }) as string;
        const holderGoodsLocationName = intl.formatMessage({ id: '请输入货位名称' }) as string;
        const holderSortIndex = intl.formatMessage({ id: '请输入显示顺序' }) as string;

        const { width } = Modal.getLayoutConfig({ columnCount: 4, maxColumns: formConfig.column });

        return (
            <Modal
                title={title}
                width={width}
                // text={text}
                open={open}
                onOk={form.submit}
                ref={ref}
                afterClose={afterClose}
                footer={footer}
                onCancel={onCancel}
            >
                <LayoutHeader>
                    <LayoutHeader.GridFormCollapse
                        id={'form'}
                        {...formConfig}
                        form={form}
                        onFinish={_onFinish}
                        onFinishFailed={_onFinishFailed}
                        defaultCollapsed={false}
                        renderSubmitter={() => null}
                        expandIcon={() => null}
                        disabled={disabled}
                        initialValues={{ status: true }}
                    >
                        <LayoutHeader.GridFormCollapse.Item
                            label={<FormatTextAuto.Locales id={'所属仓库'} />}
                            name="depotId"
                            rules={[{ required: true, message: holderName }]}
                        >
                            <GridSelect options={groupList} />
                        </LayoutHeader.GridFormCollapse.Item>
                        <LayoutHeader.GridFormCollapse.Item
                            label={<FormatTextAuto.Locales id={'货位名称'} />}
                            name="name"
                            rules={[{ required: true, message: holderGoodsLocationName }]}
                        >
                            <GridInput />
                        </LayoutHeader.GridFormCollapse.Item>
                        <LayoutHeader.GridFormCollapse.Item
                            label={<FormatTextAuto.Locales id={'显示顺序'} />}
                            name="sortIndex"
                            rules={[{ required: true, message: holderSortIndex }]}
                        >
                            <GridInputNumber style={{ width: GRID_FORM_MODAL_WIDTH }} />
                        </LayoutHeader.GridFormCollapse.Item>
                        <LayoutHeader.GridFormCollapse.Item
                            label={<FormatTextAuto.Locales id={'状态'} />}
                            name="status"
                            valuePropName="checked"
                        >
                            <GridSwitch disabled={disabled} />
                        </LayoutHeader.GridFormCollapse.Item>
                    </LayoutHeader.GridFormCollapse>
                </LayoutHeader>
            </Modal>
        );
    },
);
ModalBase.displayName = 'ModalBase';
interface ModalAddProps {
    onSubmitSuccess?: () => void;
    depotId: number;
    mode?: number;
    open?: boolean; // 新增属性
    onCancel?: () => void; // 新增属性
}
export const ModalAdd: FC<ModalAddProps> = (props) => {
    const { onSubmitSuccess, depotId, onCancel, open } = props;
    const [form] = Form.useForm();
    const intl = useIntl();
    const modalRef = useRef<ModalRefAttributes>(null);
    const [groupList, setGroupList] = useState<any>();
    useEffect(() => {
        if (open) {
            // form.setFieldValue('depotId', depotId);
            init(depotId);
        }
    }, [depotId, open]);
    const reset = () => {
        form.resetFields();
        form.setFieldValue('depotId', depotId);
    };
    const { runAsync, loading = false } = useRequest(postDepotSlotAdd, { manual: true });
    const { runAsync: getSortIndex } = useRequest(getDepotSlotGetSortIndex, {
        manual: true,
        throttleWait: DEBOUNCE_TIME,
    });
    //获取仓库列表
    const { runAsync: getDepotList } = useRequest(postDepotList, {
        throttleWait: DEBOUNCE_TIME,
        manual: true,
    });

    const onFinish = async (value: any) => {
        try {
            value.status = value.status ? 1 : 0;
            await runAsync(value, intl);
            reset();
            modalRef.current.hide();
            onSubmitSuccess();
        } catch (error) {}
    };
    const onFinishFailed = () => {};
    const holderChooseGroup = intl.formatMessage({ id: '请先选择仓库' }) as string;

    const init = async (depotId: number) => {
        try {
            if (depotId) {
                modalRef.current.show(); //弹出框
                const sortIndex = await getSortIndex({}, intl);
                form.setFieldsValue({ sortIndex: (sortIndex ?? 0) + 1 });
                const groups = await getDepotList(
                    {
                        pager: { index: 1, size: 999 },
                        sorter: [{ member: 'sortIndex', mode: 0 }],
                    },
                    intl,
                );

                const groupList = formatSelectData(groups.list, 'name', 'id');
                form.setFieldValue('depotId', depotId);

                setGroupList(groupList);
            } else {
                message.error(holderChooseGroup);
            }
        } catch (error) {}
    };
    return (
        <ModalBase
            title={<FormatTextAuto.Locales id={'新增'} />}
            open={open}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            form={form}
            ref={modalRef}
            afterClose={reset}
            okButtonProps={{ loading: loading }}
            // groupMode={mode}
            groupList={groupList}
            onCancel={onCancel}
        />
    );
};
interface ModalEditProps {
    formValue?: any;
    layoutProRef?: any;
    onSubmitSuccess?: () => void;
    text?: string; //点击列表内容时，列表上显示的字段名称,默认为修改
    onCancel?: () => void;
    open?: boolean;
}
export const ModalEdit: FC<ModalEditProps> = (props) => {
    const [form] = Form.useForm();
    const intl = useIntl();
    const { formValue, onSubmitSuccess, onCancel, open } = props;
    const [groupList, setGroupList] = useState<any>();
    // const modalRef = useRef<ModalRefAttributes>(null);
    const { runAsync, loading = false } = useRequest(postDepotSlotEdit, { manual: true });
    //获取仓库列表
    const { runAsync: getDepotList } = useRequest(postDepotList, {
        throttleWait: DEBOUNCE_TIME,
        manual: true,
    });

    const onFinish: ModalBaseProps['onFinish'] = async (value) => {
        try {
            value.status = value.status ? 1 : 0;
            await runAsync({ ...value, id: formValue?.id });
            // modalRef.current.hide();
            onSubmitSuccess();
        } catch (error) {}
    };
    useEffect(() => {
        form.setFieldsValue(formValue);
    }, [formValue]);

    const onFinishFailed: ModalBaseProps['onFinishFailed'] = () => {};
    useEffect(() => {
        if (open) {
            init();
        }
    }, [open]);

    const init = async () => {
        try {
            // layoutProRef.current.runLoading();
            const data = await getDepotSlotGet(formValue?.id, intl);
            form.setFieldsValue(data);
            const groups = await getDepotList(
                {
                    pager: { index: 1, size: 999 },
                    sorter: [{ member: 'sortIndex', mode: 0 }],
                },
                intl,
            );
            const groupList = formatSelectData(groups.list, 'name', 'id');
            setGroupList(groupList);
            // modalRef.current.show(); //弹出框
            // setData(data);
        } catch (error) {}
    };
    return (
        <ModalBase
            // ref={modalRef}
            form={form}
            title={<FormatTextAuto.Locales id={'修改'} />}
            open={open}
            onCancel={onCancel}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            okButtonProps={{ loading: loading }}
            groupList={groupList}
        />
    );
};

interface ModalLookProps {
    formValue?: any;
    layoutProRef?: any;
    onCancel?: () => void;
    open?: boolean;
}
export const ModalLook: FC<ModalLookProps> = (props) => {
    const [form] = Form.useForm();
    const intl = useIntl();
    const { formValue, onCancel, open } = props;
    const modalRef = useRef<ModalRefAttributes>(null);
    const onFinish: ModalBaseProps['onFinish'] = () => {};
    const onFinishFailed: ModalBaseProps['onFinishFailed'] = () => {};
    const [groupList, setGroupList] = useState<any>();

    useEffect(() => {
        if (open) init();
    }, [open]);
    //获取仓库列表
    const { runAsync: getDepotList } = useRequest(postDepotList, {
        throttleWait: DEBOUNCE_TIME,
        manual: true,
    });
    const init = async () => {
        try {
            // layoutProRef.current.runLoading();
            const data = await getDepotSlotGet(formValue?.id, intl);
            const groups = await getDepotList(
                {
                    pager: { index: 1, size: 999 },
                    sorter: [{ member: 'sortIndex', mode: 0 }],
                },
                intl,
            );
            form.setFieldsValue(data);
            const groupList = formatSelectData(groups.list, 'name', 'id');
            setGroupList(groupList);
            // modalRef.current.show(); //弹出框
            // setData(data);
        } catch (error) {}
    };

    return (
        <ModalBase
            ref={modalRef}
            open={open}
            onCancel={onCancel}
            form={form}
            title={<FormatTextAuto.Locales id={'查看'} />}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            footer={
                <Button
                    onClick={() => {
                        onCancel();
                    }}
                >
                    <FormatTextAuto.Locales id="关闭(Esc)" />
                </Button>
            }
            disabled
            groupList={groupList}
        />
    );
};
ModalLook.displayName = 'ModalLook';

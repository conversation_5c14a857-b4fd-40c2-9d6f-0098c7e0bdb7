import React, { FC, useRef, useState } from 'react';
import { Button, FormattedMessageSpan, GridFormCollapse, GridInput, GridSelect, Space, Tag } from '@common/components';
import { LayoutProComponentRefAttributesType, LayoutProTableProps } from '@common/components/feature';
import { LayoutTabsPro } from '@common/components/feature';
import { Form, GridTableProps } from '@weway/beacon';
import { TABLE_BG } from '@/common';

import { pick } from 'lodash';
import { useBoolean, useKeyPress, useRequest } from 'ahooks';
import defs from '@common/services/api/admin/api';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useLayoutTableTree } from '@/hooks/useLayoutTableTree';
import {
    getSubjectGet,
    postReceiveList,
    postSubjectDelete,
    postSubjectList,
    postSubjectRecycleList,
    postSubjectRecycleRestore,
} from '@common/services/api/admin/mods/finance';
import { ModalAdd, ModalEdit, ModalLook } from './components/modal-add';
import ModalConfirm from '@/components/feature/modal-confirm';

const RevenueType: FC = () => {
    const layoutProRef = useRef<LayoutProComponentRefAttributesType>(null);
    const layoutProRef1 = useRef<LayoutProComponentRefAttributesType>(null);
    const layoutProRecycleRef = useRef<LayoutProComponentRefAttributesType>(null);
    const intl = useIntl();
    const [dataSource, setDataSource] = useState<any>([]);
    const [formValue, setFormValue] = useState<any>();
    const [addOpen, { setTrue: setAddOpenTrue, setFalse: setAddOpenFalse }] = useBoolean(false);
    const [deleteOpen, { setTrue: setDeleteOpenTrue, setFalse: setDeleteOpenFalse }] = useBoolean(false);
    const [deleteRecycleOpen, { setTrue: setDeleteRecycleOpenTrue, setFalse: setDeleteRecycleOpenFalse }] =
        useBoolean(false);
    const [recycleBinOpen, { setTrue: setRecycleBinOpenTrue, setFalse: setRecycleBinOpenFalse }] = useBoolean(false);
    const [isParent, { setTrue: setIsParentTrue, setFalse: setIsParentFalse }] = useBoolean(false);
    const [editOpen, { setTrue: setEditOpenTrue, setFalse: setEditOpenFalse }] = useBoolean(false);
    const [lookOpen, { setTrue: setLookOpenTrue, setFalse: setLookOpenFalse }] = useBoolean(false);
    //切换状态
    const [form] = Form.useForm();
    //快捷键设置
    useKeyPress('alt.n', () => {});
    useKeyPress('alt.r', () => {
        form.submit();
    });
    const { runAsync: getSubject } = useRequest(getSubjectGet, {
        manual: true,
    });
    const columns: LayoutProTableProps['columns'] = [
        {
            dataIndex: 'option',
            key: 'option',
            alwaysControl: true,
            ellipsis: true,
            renderControl: (options) => (
                <Space.Group>
                    <FormattedMessageSpan
                        type="link"
                        id="查看"
                        onClick={async () => {
                            try {
                                (layoutProRef?.current || layoutProRef1?.current).runLoading();
                                const data = await getSubject(options.item.code, intl);
                                setFormValue(data);
                                (layoutProRef?.current || layoutProRef1?.current).stopLoading();
                                setLookOpenTrue();
                            } catch (error) {
                                (layoutProRef?.current || layoutProRef1?.current).stopLoading();
                            }
                        }}
                    />
                    {options.item.presetFlag !== 2 && (
                        <FormattedMessageSpan
                            type="link"
                            id="修改"
                            onClick={async () => {
                                try {
                                    layoutProRef.current.runLoading();
                                    const data = await getSubject(options.item.code, intl);
                                    setFormValue(data);
                                    layoutProRef.current.stopLoading();
                                    setEditOpenTrue();
                                } catch (error) {
                                    layoutProRef.current.stopLoading();
                                }
                            }}
                        />
                    )}
                    {options.item.presetFlag !== 2 && (
                        <FormattedMessageSpan
                            type="link"
                            id="删除"
                            onClick={() => {
                                setFormValue(options.item.code);
                                setDeleteOpenTrue();
                            }}
                        />
                    )}
                    {options.item.presetFlag !== 2 && (
                        <FormattedMessageSpan
                            type="link"
                            id="添加下级"
                            onClick={() => {
                                setFormValue(options.item.code);
                                setIsParentTrue();
                                setAddOpenTrue();
                            }}
                        />
                    )}
                </Space.Group>
            ),
            fixed: 'first',
            align: 'center',
            readonly: false,
            editableIcon: false,
        },
        {
            dataIndex: 'name',
            key: 'name',
            align: 'center',
            sorter: true,
        },
        {
            dataIndex: 'code',
            key: 'code',
            align: 'center',
        },

        {
            dataIndex: 'isEnabled',
            key: 'isEnabled',
            align: 'center',
            type: 'switch',
            alwaysControl: true,
            readonly: false,
        },
        {
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
        },
    ];
    const columns1: LayoutProTableProps['columns'] = [
        {
            dataIndex: 'option',
            key: 'option',
            alwaysControl: true,
            ellipsis: true,
            renderControl: (options) => (
                <Space.Group>
                    <FormattedMessageSpan
                        type="link"
                        id="查看"
                        onClick={async () => {
                            try {
                                (layoutProRef?.current || layoutProRef1?.current)?.runLoading();
                                const data = await getSubject(options.item.code, intl);
                                setFormValue(data);
                                (layoutProRef?.current || layoutProRef1?.current)?.stopLoading();
                                setLookOpenTrue();
                            } catch (error) {
                                (layoutProRef?.current || layoutProRef1?.current)?.stopLoading();
                            }
                        }}
                    />
                    {options.item.presetFlag !== 2 && (
                        <FormattedMessageSpan
                            type="link"
                            id="修改"
                            onClick={async () => {
                                try {
                                    (layoutProRef?.current || layoutProRef1?.current).runLoading();
                                    const data = await getSubject(options.item.code, intl);
                                    setFormValue(data);
                                    (layoutProRef?.current || layoutProRef1?.current).stopLoading();
                                    setEditOpenTrue();
                                } catch (error) {
                                    (layoutProRef?.current || layoutProRef1?.current).stopLoading();
                                }
                            }}
                        />
                    )}
                    {options.item.presetFlag !== 2 && (
                        <FormattedMessageSpan
                            type="link"
                            id="删除"
                            onClick={() => {
                                setFormValue(options.item.code);
                                setDeleteOpenTrue();
                            }}
                        />
                    )}
                    {options.item.presetFlag !== 2 && (
                        <FormattedMessageSpan
                            type="link"
                            id="添加下级"
                            onClick={() => {
                                setFormValue(options.item.code);
                                setIsParentTrue();
                                setAddOpenTrue();
                            }}
                        />
                    )}
                </Space.Group>
            ),
            fixed: 'first',
            align: 'center',
            readonly: false,
            editableIcon: false,
        },
        {
            dataIndex: 'name',
            key: 'name',
            align: 'center',
            sorter: true,
        },
        {
            dataIndex: 'code',
            key: 'code',
            align: 'center',
        },

        {
            dataIndex: 'isEnabled',
            key: 'isEnabled',
            align: 'center',
            type: 'switch',
            alwaysControl: true,
            readonly: false,
        },
        {
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
        },
    ];
    const columns_recycle: LayoutProTableProps['columns'] = [
        {
            dataIndex: 'option',
            key: 'option',
            alwaysControl: true,
            ellipsis: true,
            renderControl: (options) => (
                <Space.Group>
                    <FormattedMessageSpan
                        type="link"
                        id="查看"
                        onClick={async () => {
                            try {
                                (
                                    layoutProRef?.current ||
                                    layoutProRef1?.current ||
                                    layoutProRecycleRef?.current
                                )?.runLoading();
                                const data = await getSubject(options.item.code, intl);
                                setFormValue(data);
                                (
                                    layoutProRef?.current ||
                                    layoutProRef1?.current ||
                                    layoutProRecycleRef?.current
                                )?.stopLoading();
                                setLookOpenTrue();
                            } catch (error) {
                                (
                                    layoutProRef?.current ||
                                    layoutProRef1?.current ||
                                    layoutProRecycleRef?.current
                                )?.stopLoading();
                            }
                        }}
                    />
                    <FormattedMessageSpan
                        id="还原"
                        type="link"
                        onClick={() => {
                            setFormValue(options.item.code);
                            setRecycleBinOpenTrue();
                        }}
                    />
                    <FormattedMessageSpan
                        type="link"
                        id="删除"
                        onClick={() => {
                            setFormValue(options.item.code);
                            setDeleteRecycleOpenTrue();
                        }}
                    />
                </Space.Group>
            ),
            fixed: 'first',
            align: 'center',
        },
        {
            dataIndex: 'name',
            key: 'name',
            align: 'center',
        },
        {
            dataIndex: 'code',
            key: 'code',
            align: 'center',
            sorter: true,
        },

        {
            dataIndex: 'status',
            key: 'status',
            align: 'center',
            renderFormatter: () => {
                return <Tag type={'Delete'} />;
            },
        },
        {
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
            renderFormatter(options) {
                return (
                    <div
                        style={{
                            display: '-webkit-box',
                            WebkitBoxOrient: 'vertical',
                            WebkitLineClamp: 3,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            wordBreak: 'break-all',
                        }}
                    >
                        {options.value as string}
                    </div>
                );
            },
        },
    ];
    const { expandedRowKeys, setExpandedRowKeys, onExpandAll, onExpandRetract, onSearchTreeList, setTreeIds } =
        useLayoutTableTree();
    const handleExpand: Exclude<GridTableProps['expandable'], undefined | true>['onExpand'] = (
        expanded: any,
        item: defs.admin.Km_Business_Work_Application_Admin_Product_Catalog_CatalogList__Response__DataItem,
    ) => {
        const rowKey = item.id;
        if (expanded) {
            return setExpandedRowKeys((expandedRowKeys) => expandedRowKeys.concat(rowKey));
        }
        return setExpandedRowKeys((expandedRowKeys) => expandedRowKeys.filter((key) => key !== rowKey));
    };
    // 给dataSource添加id字段
    const addIdField = (data: Array<any>) => {
        return data.map((item) => {
            // 为当前对象添加id字段
            const newItem = {
                ...item,
                id: item.code,
            };

            // 如果有子节点，递归处理
            if (item.children && item.children.length > 0) {
                newItem.children = addIdField(item.children);
            }

            return newItem;
        });
    };
    return (
        <>
            <ModalAdd
                open={addOpen}
                onCancel={setAddOpenFalse}
                isParent={isParent}
                formValue={formValue}
                onSubmitSuccess={async () => {
                    setAddOpenFalse();
                    try {
                        (layoutProRef?.current || layoutProRef1?.current).runLoading();
                        await (layoutProRef?.current || layoutProRef1?.current).refreshRequest();
                        (layoutProRef?.current || layoutProRef1?.current).stopLoading();
                    } catch (error) {
                        (layoutProRef?.current || layoutProRef1?.current).stopLoading();
                    }
                }}
                dataSources={dataSource}
            />
            <ModalEdit
                open={editOpen}
                onCancel={setEditOpenFalse}
                onSubmitSuccess={async () => {
                    setEditOpenFalse();
                    try {
                        (layoutProRef?.current || layoutProRef1?.current).runLoading();
                        await (layoutProRef?.current || layoutProRef1?.current).refreshRequest();
                        (layoutProRef?.current || layoutProRef1?.current).stopLoading();
                    } catch (error) {
                        (layoutProRef?.current || layoutProRef1?.current).stopLoading();
                    }
                }}
                dataSources={dataSource}
                formValue={formValue}
            />
            <ModalLook open={lookOpen} onClose={setLookOpenFalse} dataSources={dataSource} formValue={formValue} />
            {/* 删除 */}
            <ModalConfirm
                type="DELETE"
                onOk={async () => {
                    try {
                        (layoutProRef?.current || layoutProRef1?.current).runLoading();
                        await postSubjectDelete({ subjectCode: formValue }, intl);
                        await (layoutProRef?.current || layoutProRef1?.current).refreshRequest();
                        (layoutProRef?.current || layoutProRef1?.current).stopLoading();
                        setDeleteOpenFalse();
                    } catch (error) {
                        (layoutProRef?.current || layoutProRef1?.current).stopLoading();
                    }
                }}
                open={deleteOpen}
                onCancel={setDeleteOpenFalse}
            />
            {/* 回收站删除 */}
            <ModalConfirm
                type="DELETE_RECYCLE"
                onOk={async () => {
                    try {
                        layoutProRecycleRef.current.runLoading();
                        await postSubjectDelete({ subjectCode: formValue }, intl);
                        await layoutProRecycleRef.current.refreshRequest();
                        layoutProRecycleRef.current.stopLoading();
                        setDeleteRecycleOpenFalse();
                    } catch (error) {
                        layoutProRecycleRef.current.stopLoading();
                    }
                }}
                open={deleteRecycleOpen}
                onCancel={setDeleteRecycleOpenFalse}
            />
            {/* 回收站还原 */}
            <ModalConfirm
                type="RECOVER"
                open={recycleBinOpen}
                onCancel={setRecycleBinOpenFalse}
                onOk={async () => {
                    try {
                        layoutProRecycleRef.current.runLoading();
                        await postSubjectRecycleRestore({ subjectCode: formValue }, intl);
                        await layoutProRecycleRef.current.refreshRequest();
                        layoutProRecycleRef.current.stopLoading();
                        setRecycleBinOpenFalse();
                    } catch (error) {
                        layoutProRecycleRef.current.stopLoading();
                    }
                }}
            />
            <LayoutTabsPro
                items={[
                    {
                        ref: layoutProRef,
                        label: <FormattedMessage id={'费用类型'} />,
                        key: '1',
                        action: (
                            <>
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        setIsParentFalse();
                                        setAddOpenTrue();
                                        setFormValue(null);
                                    }}
                                >
                                    <FormattedMessage id="新增(N)" />
                                </Button>
                                <Button>
                                    <FormattedMessage id="导入数据(Todo)" />
                                </Button>
                                <Button onClick={onExpandAll}>
                                    <FormattedMessage id="全部展开" />
                                </Button>
                                <Button onClick={onExpandRetract}>
                                    <FormattedMessage id="全部收起" />
                                </Button>
                            </>
                        ),

                        formProps: {
                            form: form,
                            fieldMeta: [
                                { name: 'keyword', label: '快速筛选', displayLabel: '快速筛选' },
                                { name: 'isEnabled', label: '状态', displayLabel: '状态' },
                            ],
                            items: [
                                {
                                    name: 'keyword',
                                    render(options) {
                                        return (
                                            <GridFormCollapse.Item
                                                label={options.label}
                                                name={options.name}
                                                key={options.name}
                                            >
                                                <GridInput placeholder={intl.formatMessage({ id: '费用名称/编号' })} />
                                            </GridFormCollapse.Item>
                                        );
                                    },
                                },
                                {
                                    name: 'isEnabled',
                                    render(options) {
                                        return (
                                            <GridFormCollapse.Item
                                                label={options.label}
                                                name={options.name}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    placeholder={intl.formatMessage({ id: '全部' })}
                                                    options={[
                                                        { label: <FormattedMessage id={'启用'} />, value: 1 },
                                                        { label: <FormattedMessage id={'禁用'} />, value: 0 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        );
                                    },
                                },
                            ],
                            id: '01',
                            expandIcon: false,
                            defaultCollapsed: true,
                            onFinish(values) {
                                const keywords = values.nameOrCode;
                                if (!keywords) {
                                    // 没有关键词时，收起全部项
                                    onExpandRetract();
                                }
                            },
                        },

                        tableProps: {
                            id: '01',
                            columns: columns,
                            rowKey: 'code',
                            columnsMeta: [
                                {
                                    title: '操作',
                                    displayTitle: '操作',
                                    key: 'option',
                                },
                                {
                                    title: '费用名称',
                                    displayTitle: '费用名称',
                                    key: 'name',
                                },
                                {
                                    title: '编号',
                                    displayTitle: '编号',
                                    key: 'code',
                                },
                                {
                                    title: '状态',
                                    displayTitle: '状态',
                                    key: 'isEnabled',
                                },

                                {
                                    title: '备注',
                                    displayTitle: '备注',
                                    key: 'remark',
                                },
                            ],
                            sortDirections: ['ascend', 'descend', 'ascend'],
                            defaultSortOrder: 'ascend',
                            defaultSortOrderField: 'code',
                            request: async (params) => {
                                try {
                                    const keywords = params.form.nameOrCode;
                                    const { list: data } = await postSubjectList({
                                        pager: {
                                            size: 999,
                                            index: 1,
                                        },
                                        ...(pick(params, ['sorter']) as Required<Pick<typeof params, 'sorter'>>),
                                        ...params.form,
                                        subjectCode: params?.foldedNav.value ? String(params?.foldedNav.value) : '6601',
                                    });

                                    const list = addIdField(data);

                                    setDataSource([
                                        {
                                            name: '销售费用',
                                            code: '6601',
                                        },
                                        {
                                            name: '管理费用费用',
                                            code: '6602',
                                        },
                                        {
                                            name: '财务费用',
                                            code: '6603',
                                        },
                                        ,
                                        ...list,
                                    ]); //设置数据源，传入弹窗
                                    setTreeIds(list);
                                    let filterList: any[] = list;
                                    if (keywords) {
                                        try {
                                            const res = onSearchTreeList(filterList, keywords);
                                            filterList = res.matchedNodes;
                                        } catch (error) {
                                            console.log(error);
                                        }
                                    }
                                    return {
                                        data: filterList,
                                        total: list.length,
                                    };
                                } catch (err) {
                                    return {
                                        data: [],
                                        total: 0,
                                    };
                                }
                            },
                            readonly: true,
                            resizable: true,
                            expandable: {
                                expandIconPosition: 'name',
                                expandedRowKeys: expandedRowKeys,
                                onExpand: handleExpand,
                            },
                            disabledBackground: TABLE_BG,
                        },
                        foldedNavProps: {
                            items: [
                                {
                                    value: 6601,
                                    key: 6601,
                                    label: '销售费用',
                                    key: 6601,
                                },
                                {
                                    value: 6602,
                                    key: 6602,
                                    label: '管理费用',
                                },
                                {
                                    value: 6603,
                                    key: 6603,
                                    label: '财务费用',
                                },
                            ],
                            defaultSelectedKeys: [6601],
                            async onEditClick(item: any, _e: any) {
                                console.log(item, 'item');
                            },
                            onDeleteClick(item: any) {
                                console.log(item, 'item');
                            },
                            onClick(item: any) {
                                console.log(item, 'item');
                            },
                        },
                    },
                    {
                        label: <FormattedMessage id={'收入类型'} />,
                        key: '2',
                        ref: layoutProRef1,
                        action: (
                            <>
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        setIsParentFalse();
                                        setAddOpenTrue();
                                        setFormValue(null);
                                    }}
                                >
                                    <FormattedMessage id="新增(N)" />
                                </Button>
                                <Button>
                                    <FormattedMessage id="导入数据(Todo)" />
                                </Button>
                                <Button onClick={onExpandAll}>
                                    <FormattedMessage id="全部展开" />
                                </Button>
                                <Button onClick={onExpandRetract}>
                                    <FormattedMessage id="全部收起" />
                                </Button>
                            </>
                        ),
                        formProps: {
                            form: form,
                            fieldMeta: [
                                { name: 'keyword', label: '快速筛选', displayLabel: '快速筛选' },
                                { name: 'isEnabled', label: '状态', displayLabel: '状态' },
                            ],
                            items: [
                                {
                                    name: 'keyword',
                                    render(options) {
                                        return (
                                            <GridFormCollapse.Item
                                                label={options.label}
                                                name={options.name}
                                                key={options.name}
                                            >
                                                <GridInput placeholder={intl.formatMessage({ id: '收入名称/编号' })} />
                                            </GridFormCollapse.Item>
                                        );
                                    },
                                },
                                {
                                    name: 'isEnabled',
                                    render(options) {
                                        return (
                                            <GridFormCollapse.Item
                                                label={options.label}
                                                name={options.name}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    placeholder={intl.formatMessage({ id: '全部' })}
                                                    options={[
                                                        { label: <FormattedMessage id={'启用'} />, value: 1 },
                                                        { label: <FormattedMessage id={'禁用'} />, value: 0 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        );
                                    },
                                },
                            ],
                            id: '02',
                            expandIcon: false,
                            defaultCollapsed: true,
                            onFinish(values) {
                                const keywords = values.nameOrCode;
                                if (!keywords) {
                                    // 没有关键词时，收起全部项
                                    onExpandRetract();
                                }
                            },
                        },
                        tableProps: {
                            id: '02',

                            columns: columns1,
                            rowKey: 'code',
                            columnsMeta: [
                                {
                                    title: '操作',
                                    displayTitle: '操作',
                                    key: 'option',
                                },
                                {
                                    title: '其他收入名称',
                                    displayTitle: '费用名称',
                                    key: 'name',
                                },
                                {
                                    title: '编号',
                                    displayTitle: '编号',
                                    key: 'code',
                                },
                                {
                                    title: '状态',
                                    displayTitle: '状态',
                                    key: 'isEnabled',
                                },

                                {
                                    title: '备注',
                                    displayTitle: '备注',
                                    key: 'remark',
                                },
                            ],
                            sortDirections: ['ascend', 'descend', 'ascend'],
                            defaultSortOrder: 'ascend',
                            defaultSortOrderField: 'code',
                            request: async (params) => {
                                try {
                                    const keywords = params.form.nameOrCode;
                                    const { list: data } = await postReceiveList({
                                        pager: {
                                            size: 999,
                                            index: 1,
                                        },
                                        ...(pick(params, ['sorter']) as Required<Pick<typeof params, 'sorter'>>),
                                        ...params.form,
                                        subjectCode: '6051',
                                    });
                                    const list = addIdField(data);

                                    setDataSource([
                                        {
                                            name: '其他业务收入',
                                            code: '6051',
                                        },

                                        ,
                                        ...list,
                                    ]); //设置数据源，传入弹窗
                                    setTreeIds(list);
                                    let filterList: any[] = list;
                                    if (keywords) {
                                        try {
                                            const res = onSearchTreeList(filterList, keywords);
                                            filterList = res.matchedNodes;
                                        } catch (error) {
                                            console.log(error);
                                        }
                                    }
                                    return {
                                        data: filterList,
                                        total: list.length,
                                    };
                                } catch (err) {
                                    return {
                                        data: [],
                                        total: 0,
                                    };
                                }
                            },
                            readonly: true,
                            resizable: true,
                            expandable: {
                                expandIconPosition: 'name',
                                expandedRowKeys: expandedRowKeys,
                                onExpand: handleExpand,
                            },
                            disabledBackground: TABLE_BG,
                        },
                    },
                    {
                        ref: layoutProRecycleRef,
                        label: <FormattedMessage id="回收站" />,
                        key: '3',

                        formProps: {
                            fieldMeta: [{ name: 'keyword', label: '快速筛选', displayLabel: '快速筛选' }],
                            items: [
                                {
                                    name: 'keyword',
                                    render(options) {
                                        return (
                                            <GridFormCollapse.Item
                                                label={options.label}
                                                name={options.name}
                                                key={options.name}
                                            >
                                                <GridInput placeholder={intl.formatMessage({ id: '科目名称/编号' })} />
                                            </GridFormCollapse.Item>
                                        );
                                    },
                                },
                            ],
                            id: '03',
                            expandIcon: false,
                            defaultCollapsed: true,
                        },
                        tableProps: {
                            columns: columns_recycle,
                            rowKey: 'id',
                            columnsMeta: [
                                {
                                    title: '操作',
                                    displayTitle: '操作',
                                    key: 'option',
                                },
                                {
                                    title: '科目名称',
                                    displayTitle: '科目名称',
                                    key: 'name',
                                },
                                {
                                    title: '编号',
                                    displayTitle: '编号',
                                    key: 'code',
                                },

                                {
                                    title: '状态',
                                    displayTitle: '状态',
                                    key: 'status',
                                },

                                {
                                    title: '备注',
                                    displayTitle: '备注',
                                    key: 'remark',
                                },
                            ],
                            defaultSortOrder: 'ascend',
                            defaultSortOrderField: 'code',
                            request: async (params) => {
                                try {
                                    const { list, pager } = await postSubjectRecycleList({
                                        ...(pick(params, ['pager', 'sorter']) as Required<
                                            Pick<typeof params, 'pager' | 'sorter'>
                                        >),
                                        ...params.form,
                                    });
                                    return {
                                        data: list,
                                        total: pager.totals,
                                    };
                                } catch (err) {
                                    return {
                                        data: [],
                                        total: 0,
                                    };
                                }
                            },
                            rowSelection: {
                                type: 'checkbox',
                            },
                            readonly: true,
                            resizable: true,
                            pagination: {},
                            disabledBackground: TABLE_BG,
                        },
                    },
                ]}
            />
        </>
    );
};
RevenueType.displayName = 'RevenueType';
export default RevenueType;

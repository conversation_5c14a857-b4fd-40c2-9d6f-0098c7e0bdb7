import { LayoutProComponentRefAttributesType } from '@common/components/feature';
import { LayoutTabsPro } from '@common/components/feature';
import {
    Button,
    FormattedMessageSpan,
    FormatTextAuto,
    GridFormCollapse,
    GridInput,
    Space,
    Tag,
} from '@common/components';
import React, { FC, useEffect, useRef, useState } from 'react';
import { ModalAdd, ModalEdit, ModalLook } from './components/modal-add';
import { GridTableColumnsType, GridTableComponentRefAttributesType, message } from '@weway/beacon';
import { GridSelect } from '@common/components';
import { DEBOUNCE_TIME, TABLE_BG } from '@/common';
import { pick } from 'lodash';
import ModalConfirm from '@/components/feature/modal-confirm';
import { useBoolean, useRequest } from 'ahooks';
import {
    postDepotList,
    postDepotSlotChangeSortIndex,
    postDepotSlotChangeStatus,
    postDepotSlotDelete,
    postDepotSlotList,
    postDepotSlotRecycleBinDelete,
    postDepotSlotRecycleBinList,
    postDepotSlotRecycleBinRestore,
} from '@common/services/api/admin/mods/inventory';
import { FormattedMessage, useIntl } from '@weway/i18n';
const GoodsAllocation: FC = () => {
    const intl = useIntl();
    const layoutProRef = useRef<LayoutProComponentRefAttributesType>(null);
    const layoutProRecycleRef = useRef<LayoutProComponentRefAttributesType>(null);
    const tableRef = useRef<GridTableComponentRefAttributesType>(null);
    const tableRecycleRef = useRef<GridTableComponentRefAttributesType>(null);
    const confirmId = useRef(undefined); //货位Id
    const confirmIdDelete = useRef(undefined); //回收站-删除
    const confirmIdRecycle = useRef(undefined); //回收站-还原

    const delMessage = intl.formatMessage({ id: '删除成功!' }) as string;
    const restoreMessage = intl.formatMessage({ id: '还原成功!' }) as string;
    const editMessage = intl.formatMessage({ id: '修改成功!' }) as string;
    const addMessage = intl.formatMessage({ id: '新增成功!' }) as string;
    const [groupData, setGroupData] = useState<any>();
    const [navActiveId, setNavActiveId] = useState();

    const [openEdit, { setTrue: setEditTrue, setFalse: setEditFalse }] = useBoolean(false); //货位-修改弹窗open
    const [openGoodsLocation, { setTrue: setOpenGoodsLocationTrue, setFalse: setOpenGoodsLocationFalse }] =
        useBoolean(false); //货位-删除弹窗open
    const [openAdd, { setTrue: setOpenAddTrue, setFalse: setOpenAddFalse }] = useBoolean(false); //货位-新增弹窗open
    const [openRecycleDelete, { setTrue: setOpenRecycleDeleteTrue, setFalse: setOpenRecycleDeleteFalse }] =
        useBoolean(false); //回收站-删除弹窗open
    const [openRecycleRecover, { setTrue: setOpenRecycleRecoverTrue, setFalse: setOpenRecycleRecoverFalse }] =
        useBoolean(false); //回收站-还原弹窗open
    const [openRecycleLook, { setTrue: setOpenRecycleLookTrue, setFalse: setOpenRecycleLookFalse }] = useBoolean(false); //回收站-货位查看弹窗open

    // 存储编辑sortIndex时的原数据
    const [editSortIndex, setEditSortIndex] = useState(-1);
    //删除
    const handleDelete = async (ids: Array<number>) => {
        try {
            layoutProRef.current.runLoading();
            setOpenGoodsLocationFalse();
            await postDepotSlotDelete({ ids: ids });
            await layoutProRef.current.refreshRequest();
            // await layoutProRecycleRef.current.refreshRequest();
            layoutProRef.current.stopLoading();
            message.success(delMessage);
        } catch (error) {
            layoutProRef.current.stopLoading();
        }
    };
    //还原
    const handleRecover = async (ids: Array<number>) => {
        try {
            layoutProRecycleRef.current.runLoading();
            setOpenRecycleRecoverFalse();
            await postDepotSlotRecycleBinRestore({ ids: ids });
            await layoutProRecycleRef.current.refreshRequest();
            await layoutProRef.current.refreshRequest();
            layoutProRecycleRef.current.stopLoading();
            message.success(restoreMessage);
        } catch (error) {
            layoutProRecycleRef.current.stopLoading();
        }
    };
    //删除回收站
    const handleDeleteRecylce = async (ids: Array<number>) => {
        try {
            layoutProRecycleRef.current.runLoading();
            setOpenRecycleDeleteFalse();
            await postDepotSlotRecycleBinDelete({ ids: ids });
            await layoutProRecycleRef.current.refreshRequest();
            layoutProRecycleRef.current.stopLoading();

            message.success(delMessage);
        } catch (error) {
            layoutProRecycleRef.current.stopLoading();
        }
    };
    //修改排序
    const { runAsync: changeSortIndex } = useRequest(postDepotSlotChangeSortIndex, { manual: true });
    //切换状态
    const { runAsync: changeStatus } = useRequest(postDepotSlotChangeStatus, { manual: true });

    const columns_goods_location: Array<GridTableColumnsType> = [
        {
            dataIndex: 'option',
            key: 'option',
            alwaysControl: true,
            ellipsis: true,
            readonly: false,
            editableIcon: false,
            renderControl: (option) => (
                <Space>
                    <FormattedMessageSpan
                        id="修改"
                        type="link"
                        onClick={() => {
                            confirmId.current = option.item.id;
                            setEditTrue();
                        }}
                    />
                    {option.item.isDefault == false && (
                        <FormatTextAuto.Locales
                            id={'删除'}
                            ellipsisTextProps={{ type: 'link' }}
                            onClick={() => {
                                confirmId.current = [option.item.id];
                                setOpenGoodsLocationTrue();
                            }}
                        />
                    )}
                </Space>
            ),
            fixed: 'first',
            align: 'center',
        },
        {
            dataIndex: 'name',
            key: 'name',
            align: 'center',
            sorter: true,
            alwaysControl: true,
            ellipsis: true,
            editableIcon: false,
            readonly: false,
            renderControl(options) {
                return (
                    <FormattedMessageSpan
                        id={options.item.name}
                        type={'link'}
                        onClick={() => {
                            confirmId.current = options.item.id;
                            setEditTrue();
                        }}
                    />
                );
            },
        },
        {
            dataIndex: 'sortIndex',
            key: 'sortIndex',
            align: 'center',
            sorter: true,
            readonly: false,
            renderFormatter(options) {
                return <FormatTextAuto ellipsisTextProps={{ type: 'link' }}>{options.value}</FormatTextAuto>;
            },
            onClick(options) {
                setEditSortIndex(options.item.sortIndex);
            },
            //失去焦点修改排序
            async onBlur(options) {
                try {
                    if (editSortIndex == options.item.sortIndex) {
                        setEditSortIndex(-1);
                        return;
                    }
                    layoutProRef.current.runLoading();
                    await changeSortIndex({ id: options?.item?.id, sortIndex: Number(options?.value) });
                    await layoutProRef.current.refreshRequest();
                    layoutProRef.current.stopLoading();
                    setEditSortIndex(-1);
                } catch (error) {
                    layoutProRef.current.stopLoading();
                    setEditSortIndex(-1);
                }
            },
        },
        {
            dataIndex: 'depotName',
            key: 'depotName',
            align: 'center',
            // sorter: true,超哥：企微5/26 16：24分，确认不要排序
        },
        {
            dataIndex: 'status',
            key: 'status',
            align: 'center',
            type: 'switch',
            alwaysControl: true,
            readonly: false,
            editableIcon: false,
            async onChange(options) {
                try {
                    layoutProRef.current.runLoading();
                    await changeStatus({ id: options?.item?.id, status: options?.value ? 1 : 0 });
                    await layoutProRef.current.refreshRequest();
                    layoutProRef.current.stopLoading();
                } catch (error) {
                    layoutProRef.current.stopLoading();
                }
            },
        },
        {
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
            renderFormatter(options) {
                return (
                    <div
                        style={{
                            display: '-webkit-box',
                            WebkitBoxOrient: 'vertical',
                            WebkitLineClamp: 3,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            wordBreak: 'break-all',
                        }}
                    >
                        {options.value as string}
                    </div>
                );
            },
        },
    ];
    const columns_recycle: Array<GridTableColumnsType> = [
        {
            dataIndex: 'option',
            key: 'option',
            alwaysControl: true,
            ellipsis: true,
            readonly: false,
            editableIcon: false,
            renderControl: (option) => (
                <Space>
                    <FormattedMessageSpan
                        id="还原"
                        type="link"
                        onClick={() => {
                            confirmIdRecycle.current = [option.item.id];
                            setOpenRecycleRecoverTrue();
                        }}
                    />
                    <FormattedMessageSpan
                        id={'删除'}
                        type="link"
                        onClick={() => {
                            confirmIdDelete.current = [option.item.id];
                            setOpenRecycleDeleteTrue();
                        }}
                    />
                </Space>
            ),
            fixed: 'first',
            align: 'center',
        },
        {
            dataIndex: 'name',
            key: 'name',
            align: 'center',
            sorter: true,
            alwaysControl: true,
            ellipsis: true,
            editableIcon: false,
            readonly: false,
            renderControl(options) {
                return (
                    <FormattedMessageSpan
                        id={options.item.name}
                        type={'link'}
                        onClick={() => {
                            confirmIdRecycle.current = options.item.id;
                            setOpenRecycleLookTrue();
                        }}
                    />
                );
            },
        },
        {
            dataIndex: 'sortIndex',
            key: 'sortIndex',
            align: 'center',
            sorter: true,
        },
        {
            dataIndex: 'depotName',
            key: 'depotName',
            align: 'center',
            sorter: true,
        },
        {
            dataIndex: 'status',
            key: 'status',
            align: 'center',
            renderFormatter: () => {
                return <Tag type={'Delete'} />;
            },
        },
    ];

    //获取仓库列表
    const { runAsync: getDepotList } = useRequest(postDepotList, {
        throttleWait: DEBOUNCE_TIME,
        manual: true,
    });
    useEffect(() => {
        getWarehouseList();
    }, []);
    const getWarehouseList = async () => {
        const groups = await getDepotList({
            pager: { index: 1, size: 999 },
            sorter: [{ member: 'sortIndex', mode: 0 }],
        });
        const data: Array<any> = groups?.list?.map((item) => ({
            label: item.name,
            key: item.id,
            id: item.id,
            readonly: true,
        }));
        data?.unshift({
            label: <FormatTextAuto.Locales id={'全部'} />,
            key: 0,
            id: null,
            readonly: true,
        });
        setGroupData([...data]);
    };

    return (
        <>
            <LayoutTabsPro
                items={[
                    {
                        ref: layoutProRef,
                        label: <FormattedMessage id={'货位列表'} />,
                        key: '1',
                        action: (
                            <>
                                <Button
                                    onClick={() => {
                                        if (!navActiveId) {
                                            message.error(intl.formatMessage({ id: '请选择仓库' }));
                                            return;
                                        }
                                        setOpenAddTrue();
                                    }}
                                    type="primary"
                                >
                                    <FormatTextAuto.Locales id={'新增(N)'} />
                                </Button>

                                <Button>
                                    <FormatTextAuto.Locales id="导入数据(to do)"></FormatTextAuto.Locales>
                                </Button>
                                <Button
                                    onClick={() => {
                                        const data = tableRef.current.getSelectItems();
                                        if (data?.length) {
                                            setOpenGoodsLocationTrue();
                                            confirmId.current = data.map((item) => item.id);
                                        } else {
                                            message.error(intl.formatMessage({ id: '请选择' }));
                                        }
                                    }}
                                >
                                    <FormatTextAuto.Locales id={'批量删除'} />
                                </Button>
                            </>
                        ),
                        formProps: {
                            fieldMeta: [
                                { name: 'keywords', label: '快速筛选', displayLabel: '快速筛选' },
                                { name: 'depotName', label: '仓库', displayLabel: '仓库' },
                                { name: 'status', label: '状态', displayLabel: '状态' },
                            ],
                            items: [
                                {
                                    name: 'keywords',
                                    render(options) {
                                        return (
                                            <GridFormCollapse.Item
                                                label={options.label}
                                                name={options.name}
                                                key={options.name}
                                            >
                                                <GridInput
                                                    placeholder={intl.formatMessage({ id: '货位名称' }) as string}
                                                />
                                            </GridFormCollapse.Item>
                                        );
                                    },
                                },
                                {
                                    name: 'depotName',
                                    render(options) {
                                        return (
                                            <GridFormCollapse.Item
                                                label={options.label}
                                                name={options.name}
                                                key={options.name}
                                            >
                                                <GridInput />
                                            </GridFormCollapse.Item>
                                        );
                                    },
                                },
                                {
                                    name: 'status',
                                    render(options) {
                                        return (
                                            <GridFormCollapse.Item
                                                label={options.label}
                                                name={options.name}
                                                key={options.name}
                                            >
                                                <GridSelect
                                                    options={[
                                                        { label: <FormatTextAuto.Locales id={'全部'} />, value: null },
                                                        { label: <FormatTextAuto.Locales id={'启用'} />, value: 1 },
                                                        { label: <FormatTextAuto.Locales id={'禁用'} />, value: 0 },
                                                    ]}
                                                />
                                            </GridFormCollapse.Item>
                                        );
                                    },
                                },
                            ],
                            id: '01',
                            expandIcon: false,
                            defaultCollapsed: true,
                            initialValues: { status: null },
                        },
                        tableProps: {
                            id: '01',
                            columns: columns_goods_location,
                            rowKey: 'id',
                            ref: tableRef,
                            columnsMeta: [
                                {
                                    title: '操作',
                                    displayTitle: '操作',
                                    key: 'option',
                                },
                                {
                                    title: '货位名称',
                                    displayTitle: '货位名称',
                                    key: 'name',
                                },
                                {
                                    title: '显示排序',
                                    displayTitle: '显示排序',
                                    key: 'sortIndex',
                                },
                                {
                                    title: '所属仓库 ',
                                    displayTitle: '所属仓库 ',
                                    key: 'depotName',
                                },
                                {
                                    title: '状态',
                                    displayTitle: '状态',
                                    key: 'status',
                                },
                            ],
                            defaultSortOrder: 'ascend',
                            defaultSortOrderField: 'sortIndex',
                            sortDirections: ['ascend', 'descend', 'ascend'],
                            request: async (params) => {
                                const { keywords, depotName, ...otherParams } = params?.form;
                                try {
                                    const { list, pager } = await postDepotSlotList({
                                        ...(pick(params, ['pager', 'sorter']) as Required<
                                            Pick<typeof params, 'pager' | 'sorter'>
                                        >),

                                        ...otherParams,
                                        ...(keywords && { keywords: String(keywords).trim() }),
                                        ...(depotName && { depotName: String(depotName).trim() }),
                                        depotId: params?.foldedNav?.id,
                                    });
                                    return {
                                        data: list,
                                        total: pager.totals,
                                    };
                                } catch (err) {
                                    return {
                                        data: [],
                                        total: 0,
                                    };
                                }
                            },
                            rowSelection: {
                                type: 'checkbox',
                            },
                            readonly: true,
                            resizable: true,
                            disabledBackground: TABLE_BG,
                            pagination: {},
                        },
                        foldedNavProps: {
                            readonly: false,
                            items: groupData,
                            defaultSelectedKeys: [0],
                            onClick(_item, x: any) {
                                setNavActiveId(x?.id as any);
                            },
                            onSelect(selectedKeys) {
                                setNavActiveId(selectedKeys?.[0] as any);
                            },
                        },
                    },
                    {
                        ref: layoutProRecycleRef,
                        label: <FormattedMessage id="回收站" />,
                        key: '2',
                        action: (
                            <>
                                <Button
                                    onClick={() => {
                                        const data = tableRecycleRef.current.getSelectItems();
                                        if (data?.length) {
                                            setOpenRecycleRecoverTrue();
                                            confirmIdRecycle.current = data.map((item) => item.id);
                                        } else {
                                            message.error(intl.formatMessage({ id: '请选择' }));
                                        }
                                    }}
                                >
                                    <FormattedMessage id={'批量还原'} />
                                </Button>
                                <Button
                                    onClick={() => {
                                        const data = tableRecycleRef.current.getSelectItems();
                                        if (data?.length) {
                                            setOpenRecycleDeleteTrue();
                                            confirmIdDelete.current = data.map((item) => item.id);
                                        } else {
                                            message.error(intl.formatMessage({ id: '请选择' }));
                                        }
                                    }}
                                >
                                    <FormattedMessage id={'批量删除'} />
                                </Button>
                            </>
                        ),
                        formProps: {
                            fieldMeta: [{ name: 'keywords', label: '快速筛选', displayLabel: '快速筛选' }],
                            items: [
                                {
                                    name: 'keywords',
                                    render(options) {
                                        return (
                                            <GridFormCollapse.Item
                                                label={options.label}
                                                name={options.name}
                                                key={options.name}
                                            >
                                                <GridInput placeholder={intl.formatMessage({ id: '货位名称' })} />
                                            </GridFormCollapse.Item>
                                        );
                                    },
                                },
                            ],
                            id: '02',
                            expandIcon: false,
                            defaultCollapsed: true,
                        },
                        tableProps: {
                            id: '02',
                            columns: columns_recycle,
                            ref: tableRecycleRef,
                            columnsMeta: [
                                {
                                    title: '操作',
                                    displayTitle: '操作',
                                    key: 'option',
                                },
                                {
                                    title: '货位名称',
                                    displayTitle: '货位名称',
                                    key: 'name',
                                },
                                {
                                    title: '显示排序',
                                    displayTitle: '显示排序',
                                    key: 'sortIndex',
                                },
                                {
                                    title: '所属仓库',
                                    displayTitle: '所属仓库',
                                    key: 'depotName',
                                },
                                {
                                    title: '状态',
                                    displayTitle: '状态',
                                    key: 'status',
                                },
                            ],
                            rowKey: 'id',
                            defaultSortOrder: 'ascend',
                            defaultSortOrderField: 'sortIndex',
                            sortDirections: ['ascend', 'descend', 'ascend'],
                            request: async (params) => {
                                const { keywords, ...otherParams } = params.form;
                                try {
                                    const { data, pager } = await postDepotSlotRecycleBinList({
                                        ...(pick(params, ['pager', 'sorter']) as Required<
                                            Pick<typeof params, 'pager' | 'sorter'>
                                        >),
                                        ...(keywords && { keywords: String(keywords).trim() }),
                                        ...otherParams,
                                    });
                                    return {
                                        data: data,
                                        total: pager.totals,
                                    };
                                } catch (err) {
                                    return {
                                        data: [],
                                        total: 0,
                                    };
                                }
                            },
                            rowSelection: {
                                type: 'checkbox',
                            },
                            readonly: true,
                            resizable: true,
                            pagination: {},
                            disabledBackground: TABLE_BG,
                        },
                    },
                ]}
            />
            {/* 货位-新增 */}
            <ModalAdd
                depotId={navActiveId}
                onSubmitSuccess={() => {
                    message.success(addMessage);
                    layoutProRef.current.refreshRequest();
                }}
                open={openAdd}
                onCancel={setOpenAddFalse}
            />
            {/* <ModalConfirm
                handleOk={() => handleDelete(tableRef.current.getSelectItems().map((item) => item.id))}
                tableRef={tableRef}
            /> */}
            {/* 货位-删除 */}
            <ModalConfirm
                type="DELETE"
                onOk={() => handleDelete(confirmId.current)}
                open={openGoodsLocation}
                onCancel={setOpenGoodsLocationFalse}
            />
            {/* 回收站- 还原 */}
            <ModalConfirm
                type="RECOVER"
                onOk={() => handleRecover(confirmIdRecycle.current)}
                open={openRecycleRecover}
                onCancel={setOpenRecycleRecoverFalse}
            />
            {/* 回收站- 删除 */}
            <ModalConfirm
                type="DELETE_RECYCLE"
                onOk={() => handleDeleteRecylce(confirmIdDelete.current)}
                open={openRecycleDelete}
                onCancel={setOpenRecycleDeleteFalse}
            />
            {/* 货位-修改 */}
            <ModalEdit
                open={openEdit}
                formValue={{ id: confirmId.current }}
                layoutProRef={layoutProRef}
                onSubmitSuccess={() => {
                    //更新成功后刷新列表
                    message.success(editMessage);
                    setEditFalse();
                    try {
                        layoutProRef.current.runLoading();
                        layoutProRef.current.refreshRequest();
                        layoutProRef.current.stopLoading();
                    } catch (error) {
                        layoutProRef.current.stopLoading();
                    }
                }}
                onCancel={setEditFalse}
            />
            {/*回收站- 批量删除 */}
            <ModalConfirm
                type="DELETE_RECYCLE"
                onOk={() => handleDeleteRecylce(confirmIdDelete.current)}
                open={openRecycleDelete}
                onCancel={setOpenRecycleDeleteFalse}
            />
            {/*回收站- 批量还原 */}
            <ModalConfirm
                type="RECOVER"
                onOk={() => handleRecover(confirmIdRecycle.current)}
                open={openRecycleRecover}
                onCancel={setOpenRecycleRecoverFalse}
            />
            {/* 回收站-货位查看 */}
            <ModalLook
                open={openRecycleLook}
                layoutProRef={layoutProRecycleRef}
                formValue={{ id: confirmIdRecycle.current }}
                onCancel={setOpenRecycleLookFalse}
            />
        </>
    );
};
GoodsAllocation.displayName = 'GoodsAllocation';
export default GoodsAllocation;

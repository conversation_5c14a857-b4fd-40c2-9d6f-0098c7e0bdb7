import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { DocumentDetailProps } from './data';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { GridDatePicker, GridForm, GridInputNumber, GridSwitch } from '@weway/beacon';
import { GridFormCollapse, Space, Tag, GridInput, GridSelect, Text } from '@common/components';
import Footer from './footer';
import DocumentTable from './DocumentTable';
import dayjs from 'dayjs';
import { TRANSFER_TYPE } from '@/common/message';
import { ConfigGridForm } from '@common/components/feature';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
import { SelectDepartment } from '@/components/feature/basic-infomation/select-department';
import { SelectDepot } from '@/components/feature/basic-infomation/select-depot';
/**
 * 单据详情
 * @returns
 */
const DocumentDetail = forwardRef((props: DocumentDetailProps, ref: any) => {
    const { getConfigSettingContainer, onFinish, datas } = props;
    const intl = useIntl();
    const tabRef = useRef<any>();
    const footerRef = useRef<any>();
    const [form] = GridForm.useForm();
    const isChangePrice = GridForm.useWatch('isChangePrice', form);
    const submitData: any = {
        formData: {},
        tableData: [],
        footerData: {},
    };

    useEffect(() => {
        if (!datas?.id) {
            //处理点击新增按钮时，表单重置
            form.resetFields();
        }
        form.setFieldsValue(datas);
    }, [datas]);

    useImperativeHandle(ref, () => ({
        async submit() {
            form.submit();
        },
    }));
    return (
        <>
            <ConfigGridForm
                getConfigSettingContainer={getConfigSettingContainer}
                labelCol={{ span: 8 }}
                form={form}
                configId=""
                renderSubmitter={() => null}
                defaultCollapsed={true}
                onFinish={(values: any) => {
                    tabRef.current.submit();

                    footerRef.current.submit();
                    submitData.formData = values;
                    onFinish(submitData);
                }}
                initialValues={{
                    submittedTime: dayjs(),
                    transferType: 1,
                }}
                formItems={[
                    {
                        name: 'businessNumber',
                        label: '单据编号',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                >
                                    <Space>
                                        <Text type="primary">{datas?.businessNumber}</Text>
                                        {datas?.status === 40 && <Tag type={'WaitForReceiving'} />}
                                        {datas?.status === 20 && <Tag type={'ReadyForShipment'} />}
                                        {datas?.status === 25 && <Tag type={'PartialDelivery'} />}
                                        {datas?.status === 100 && <Tag type={'Done'} />}
                                    </Space>
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'submittedTime',
                        label: '单据日期',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择日期' }) }]}
                                >
                                    <GridDatePicker style={{ width: '100%' }} />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'transferType',
                        label: '调拨类型',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    label={options.label}
                                    name={options.name}
                                    key={options.name}
                                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择调拨类型' }) }]}
                                >
                                    <GridSelect
                                        options={[
                                            {
                                                label: <FormattedMessage id={TRANSFER_TYPE[1]} />,
                                                value: 1,
                                            },
                                            {
                                                label: <FormattedMessage id={TRANSFER_TYPE[2]} />,
                                                value: 2,
                                            },
                                            {
                                                label: <FormattedMessage id={TRANSFER_TYPE[3]} />,
                                                value: 3,
                                            },
                                        ]}
                                    />
                                </GridFormCollapse.Item>
                            );
                        },
                    },

                    {
                        name: 'handlerId',
                        label: '经手人',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    column={1}
                                    name={options.name}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择经手人' }) }]}
                                >
                                    <SelectUser
                                        placeholder={intl.formatMessage({ id: '请选择' })}
                                        onChange={(_e, obj: any) => {
                                            //选择经手人，部门自动填充
                                            form.setFieldValue('departmentId', obj?.departmentId);
                                        }}
                                    />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'departmentId',
                        label: '部门',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                >
                                    <SelectDepartment />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'fromDepotId',
                        label: '调出仓库',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    column={1}
                                    name={options.name}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择调出仓库' }) }]}
                                >
                                    <SelectDepot />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'toDepotId',
                        label: '调入仓库',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    column={1}
                                    name={options.name}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择调入仓库' }) }]}
                                >
                                    <SelectDepot />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'isReceived',
                        label: '已收货入库',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    column={1}
                                    name={options.name}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                    valuePropName="checked"
                                >
                                    <GridSwitch />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'deliveryMode',
                        label: '承运方式',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    column={1}
                                    name={options.name}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                >
                                    <GridSelect
                                        options={[
                                            {
                                                label: intl.formatMessage({ id: '商家配送' }),
                                                value: 3,
                                            },
                                            {
                                                label: intl.formatMessage({ id: '物流快递' }),
                                                value: 1,
                                            },
                                            {
                                                label: intl.formatMessage({ id: '上门自提' }),
                                                value: 2,
                                            },
                                        ]}
                                        disabled={datas?.status === 40 || datas?.status === 100}
                                    />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'deliveryName',
                        label: '承运单位',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                >
                                    <GridInput disabled={datas?.status === 40} />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'deliveryPhone',
                        label: '承运电话',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                >
                                    <GridInput disabled={datas?.status === 40 || datas?.status === 100} />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'isChangePrice',
                        label: '变价调拨',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    valuePropName="checked"
                                    label={<FormattedMessage id={options.displayLabel} />}
                                >
                                    <GridSwitch />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'brief',
                        label: '单据说明',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                >
                                    <GridInput />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                    {
                        name: 'packQty',
                        label: '打包件数',
                        render: (options) => {
                            return (
                                <GridFormCollapse.Item
                                    name={options.name}
                                    column={1}
                                    label={<FormattedMessage id={options.displayLabel} />}
                                >
                                    <GridInputNumber />
                                </GridFormCollapse.Item>
                            );
                        },
                    },
                ]}
            />
            <div style={{ marginTop: 10, marginBottom: 10 }}>
                <DocumentTable
                    ref={tabRef}
                    datas={datas?.items}
                    status={datas?.status}
                    isChangePrice={isChangePrice}
                    onFinish={(tableData) => {
                        submitData.tableData = tableData;
                    }}
                />
            </div>
            <Footer
                ref={footerRef}
                datas={datas}
                onFinish={(values: any) => {
                    submitData.footerData = values;
                }}
            />
        </>
    );
});

export default DocumentDetail;
DocumentDetail.displayName = 'DocumentDetail';

import { Button, FormattedMessageSpan, FormatTextAuto, Modal, Text } from '@common/components';
import { Input, Space, Spin } from '@weway/beacon';
import React, { useEffect } from 'react';
import { ReadOnlyDataCard } from '@/components/feature/read-only-data-card';
import { ConfigGridTable, LayoutRow } from '@common/components/feature';
import { useRequest } from 'ahooks';
import { useIntl } from '@weway/i18n';
import { getPaymentBillGet } from '@common/services/api/admin/mods/finance';
import dayjs from 'dayjs';
/**
 * 收款单详情
 * @returns
 */
type ModalReceiveDetailProps = {
    open: boolean;
    id: number;
    onClose: () => void;
};
const ModalReceiveDetail = (props: ModalReceiveDetailProps) => {
    const { open, onClose, id } = props;

    const intl = useIntl();

    const handleOk = async () => {};

    const { runAsync, data } = useRequest(getPaymentBillGet, {
        manual: true,
    });
    const init = async () => {
        try {
            await runAsync(id, intl);
        } catch (error) {}
    };
    useEffect(() => {
        if (open) {
            init();
        }
    }, [open]);
    return (
        <Modal
            title={<FormattedMessageSpan id="收款单详情" />}
            open={open}
            width={'1180'}
            onOk={handleOk}
            onCancel={onClose}
            footer={
                <Space>
                    <Button onClick={onClose}>
                        <FormattedMessageSpan id="取消(Esc)" />
                    </Button>
                </Space>
            }
            destroyOnClose
        >
            <Spin spinning={false}>
                <ReadOnlyDataCard
                    style={{ marginBottom: 10 }}
                    dataSource={{
                        businessNumber: data?.result?.businessNumber,
                        submittedTime: dayjs.unix(data?.result?.submittedTime as any).format('YYYY-MM-DD HH:mm:ss'),
                        traderName: data?.result?.traderName,
                        totalAmount: data?.result?.totalAmount,
                        payAccount1Name: data?.result?.payAccount1Name,
                        payAccount2Name: data?.result?.payAccount2Name,
                        handlerName: data?.result?.handlerName,
                        departmentName: data?.result?.departmentName,
                        remark: data?.result?.remark,
                    }}
                    columns={[
                        {
                            title: <FormatTextAuto.Locales id="单据编号" />,
                            key: 'businessNumber',
                            span: 8,
                            render: (option) => {
                                return <Text type="primary">{option.value}</Text>;
                            },
                        },
                        {
                            title: <FormatTextAuto.Locales id="收款时间" />,
                            key: 'submittedTime',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="客户名称" />,
                            key: 'traderName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="收款金额" />,
                            key: 'totalAmount',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="收款账户1" />,
                            key: 'payAccount1Name',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="收款账户2" />,
                            key: 'payAccount2Name',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="支付方式" />,
                            key: 'payAccount2Name',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="发起类型" />,
                            key: 'payAccount2Name',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="发起终端" />,
                            key: 'payAccount2Name',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="经手人" />,
                            key: 'handlerName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="部门" />,
                            key: 'departmentName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="交易通道" />,
                            key: 'departmentName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="交易流水" />,
                            key: 'departmentName',
                            span: 8,
                        },
                        {
                            title: <FormatTextAuto.Locales id="备注" />,
                            key: 'remark',
                            span: 8,
                        },
                    ]}
                />
                <LayoutRow>
                    <FormattedMessageSpan style={{ fontWeight: 'bold' }} id="收款单据" />:
                </LayoutRow>
                <ConfigGridTable
                    configId=""
                    resizable
                    columns={[
                        {
                            title: '单据类型',
                            dataIndex: 'type',
                            key: 'type',
                            align: 'center',
                        },
                        {
                            title: '单据日期',
                            dataIndex: 'fromDate',
                            key: 'fromDate',
                            align: 'center',
                            renderFormatter(options) {
                                return dayjs.unix(options.value as any).format('YYYY-MM-DD');
                            },
                        },
                        {
                            title: '单据编号',
                            dataIndex: 'fromBusinessNumber',
                            key: 'fromBusinessNumber',
                            align: 'center',
                        },
                        {
                            title: '单据金额',
                            dataIndex: 'amount',
                            key: 'amount',
                            align: 'center',
                        },
                        {
                            title: '已收款金额',
                            dataIndex: 'paidAmount',
                            key: 'paidAmount',
                            align: 'center',
                        },
                        {
                            title: '欠款金额',
                            dataIndex: 'unpaidAmount',
                            key: 'unpaidAmount',
                            align: 'center',
                        },
                        {
                            title: '本次收款',
                            dataIndex: 'payAmount',
                            key: 'payAmount',
                            align: 'center',
                        },
                    ]}
                    rowKey={'code'}
                    dataSource={data?.result?.items || []}
                    editableIcon={false}
                    readonly={true}
                />
                <LayoutRow>
                    <FormattedMessageSpan style={{ fontWeight: 'bold' }} id="操作日志" />:
                </LayoutRow>
                <ConfigGridTable
                    configId=""
                    resizable
                    columns={[
                        {
                            title: '操作',
                            dataIndex: 'options',
                            key: 'options',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作时间',
                            dataIndex: 'creationTime',
                            key: 'creationTime',
                            align: 'center',
                            type: 'date-picker',
                            summary: false,
                        },
                        {
                            title: '操作人',
                            dataIndex: 'createdName',
                            key: 'createdName',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作ip',
                            dataIndex: 'ipAddress',
                            key: 'ipAddress',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作类型',
                            dataIndex: 'operationType',
                            key: 'operationType',
                            align: 'center',
                            summary: false,
                        },
                        {
                            title: '操作日志',
                            dataIndex: 'content',
                            key: 'content',
                            align: 'center',
                            summary: false,
                        },
                    ]}
                    rowKey={'code'}
                    dataSource={data?.result?.logs || []}
                    editableIcon={false}
                    readonly={true}
                    scroll={{ y: 300 }}
                />
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span>
                        <FormattedMessageSpan id="追加日志" />：
                    </span>
                    <Input.TextArea style={{ width: 400, marginRight: 10 }} />
                    <Button type="primary">
                        <FormattedMessageSpan id="确认追加" />
                    </Button>
                </div>
            </Spin>
        </Modal>
    );
};
ModalReceiveDetail.displayName = 'ModalReceiveDetail';
export default ModalReceiveDetail;

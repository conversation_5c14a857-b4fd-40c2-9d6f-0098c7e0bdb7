import { GRID_FORM_MODAL_WIDTH } from '@/common';
import { FormatTextAuto, Modal, GridInput, GridFormCollapse, Text } from '@common/components';
import { Form, GridInputNumber, GridRadio } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useRequest } from 'ahooks';
import React, { useEffect } from 'react';
import { postStockAdjust } from '@common/services/api/admin/mods/inventory';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
type ModalPopProps = {
    open: boolean;
    formValue?: any;
    handleModalResult?: (result: 'ok' | 'cancle') => void;
};
const ModalFundAdjustment = (props: ModalPopProps) => {
    const [form] = Form.useForm();
    const { open = false, formValue, handleModalResult } = props;
    const intl = useIntl();
    //保存
    const _onFinish = async (values: any) => {
        try {
            await runAsync(
                {
                    skuId: formValue?.skuId,
                    spuId: formValue?.spuId,
                    ...values,
                },
                intl,
            );
            form.resetFields();
            handleModalResult('ok');
        } catch (error) {}
    };
    const handleCancel = () => {
        handleModalResult('cancle');
    };
    const formConfig = GridFormCollapse.getLayoutConfig({
        type: 'modal-small',
        columnCount: 5,
    });
    const { width } = Modal.getLayoutConfig({ columnCount: 5, maxColumns: formConfig.column });
    const { loading, runAsync } = useRequest(postStockAdjust, {
        manual: true,
    });
    useEffect(() => {}, [formValue, open]);
    return (
        <Modal
            title={<FormatTextAuto.Locales id="资金调整" />}
            open={open}
            onOk={form.submit}
            width={width}
            onCancel={handleCancel}
            okButtonProps={{ loading }}
        >
            <GridFormCollapse
                {...formConfig}
                form={form}
                defaultCollapsed={false}
                renderSubmitter={() => null}
                expandIcon={() => null}
                initialValues={{ mode: 0 }}
                onFinish={_onFinish}
            >
                <GridFormCollapse.Item label={<FormatTextAuto.Locales id={'账户'} />} name="name">
                    <Text type="danger">{formValue?.name}</Text>
                </GridFormCollapse.Item>

                <GridFormCollapse.Item
                    label={<FormatTextAuto.Locales id={'调整类型'} />}
                    name="adjustmentType"
                    rules={[{ required: true, message: intl.formatMessage({ id: '请选择调整类型' }) }]}
                >
                    <GridRadio.Group
                        options={[
                            { label: <FormattedMessage id="调增" />, value: 1 },
                            { label: <FormattedMessage id="调减" />, value: -1 },
                        ]}
                    />
                </GridFormCollapse.Item>
                <GridFormCollapse.Item
                    label={<FormatTextAuto.Locales id={'调整金额'} />}
                    name="adjustmentStock"
                    rules={[{ required: true, message: '请输入调整金额' }]}
                >
                    <GridInputNumber style={{ width: GRID_FORM_MODAL_WIDTH }} />
                </GridFormCollapse.Item>

                <GridFormCollapse.Item
                    label={<FormatTextAuto.Locales id={'经手人'} />}
                    name="realQty"
                    rules={[{ required: true, message: '请选择经手人' }]}
                >
                    <SelectUser />
                </GridFormCollapse.Item>

                <GridFormCollapse.Item label={<FormatTextAuto.Locales id={'备注'} />} name="remark">
                    <GridInput.TextArea />
                </GridFormCollapse.Item>
            </GridFormCollapse>
        </Modal>
    );
};
export default ModalFundAdjustment;
ModalFundAdjustment.displayName = 'ModalFundAdjustment';

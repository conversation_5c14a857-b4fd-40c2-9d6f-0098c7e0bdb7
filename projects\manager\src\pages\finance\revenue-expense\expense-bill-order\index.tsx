import React, { useEffect, useRef, useState } from 'react';
import { Button, Space } from '@common/components';
import { AuthComponent } from '@common/components/feature';
import { message, Spin, Tabs } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useKeyPress, useRequest } from 'ahooks';

import { useTags } from '@weway/ui';
import { useLocation } from 'react-router-dom';
import dayjs from 'dayjs';
import { postLatestcodeGet } from '@common/services/api/admin/mods/basic';
import DocumentDetail from './components/document-detail';
import {
    getFeeIncomeGet,
    postFeeIncomeAddOrSubmit,
    postFeeIncomeEditOrSubmit,
} from '@common/services/api/admin/mods/finance';

/**
 * 费用单
 * @returns
 */
const ExpenseBillOrder = () => {
    const { navigate } = useTags();
    const { state } = useLocation(); //接收参数
    const portalTarget = useRef<HTMLDivElement>(null);
    const intl = useIntl();
    const [mData, setMData] = useState<any>({});
    const ref = useRef<any>();
    const billStatus = useRef(0);

    //生成单据编号
    const { runAsync: runAsyncGetCode } = useRequest(postLatestcodeGet, { manual: true });
    //获取详情
    const { loading: loadingGet, runAsync: runAsyncGet } = useRequest(getFeeIncomeGet, { manual: true });
    const onChange = (key: string) => {
        console.log(key);
    };
    const handleInit = async () => {
        try {
            const data: any = await runAsyncGet(state?.id, intl);
            data.id = state?.id;
            setMData(data);
        } catch (error) {}
    };
    const initCode = async () => {
        try {
            const { value } = await runAsyncGetCode({ type: 801 }, intl);
            setMData({
                ...mData,
                businessNumber: value,
                businessDate: dayjs(),
            });
        } catch (error) {}
    };
    useEffect(() => {
        if (state?.id) {
            handleInit();
        } else {
            initCode();
        }
    }, [state?.id]);

    const handlerAdd = async () => {
        try {
            const { value } = await runAsyncGetCode({ type: 202 }, intl);
            setMData({
                businessNumber: value,
                businessDate: dayjs().format('YYYY-MM-DD'),
                departmentId: null,
                depotId: null,
                handlerId: null,
                status: 0,
                billStatus: 0,
                remark: '',
                brief: '',
                items: [],
                payments: [],
            });
        } catch (error) {}
    };

    useKeyPress('alt.p', () => {});

    useKeyPress('alt.d', () => {});

    useKeyPress('alt.y', () => {});
    //新增
    const { loading: loadingAdd, runAsync: runAsyncAdd } = useRequest(postFeeIncomeAddOrSubmit, { manual: true });
    //修改
    const { loading: loadingUpdate, runAsync: runAsyncUpdate } = useRequest(postFeeIncomeEditOrSubmit, {
        manual: true,
    });
    return (
        <Spin spinning={loadingGet || loadingAdd || loadingUpdate}>
            <Tabs
                defaultActiveKey="1"
                items={[
                    {
                        key: '1',
                        label: <FormattedMessage id={'费用单'} />,
                        children: (
                            <DocumentDetail
                                getConfigSettingContainer={() => portalTarget}
                                ref={ref}
                                datas={mData}
                                isCopy={state?.isCopy}
                                onFinish={async (value: any) => {
                                    console.log(value);

                                    value.formData.businessDate = dayjs(value.formData.businessDate).unix();
                                    value.formData.departmentId = Array.isArray(value.formData.departmentId)
                                        ? value.formData.departmentId[value.formData.departmentId.length - 1]
                                        : value.formData.departmentId;
                                    const newData = {
                                        ...value.formData,
                                        ...value.footerData,
                                        type: 801,
                                        items: value.tableData
                                            .filter((its: any) => its.code)
                                            .map((item: any) => {
                                                return {
                                                    code: item.code,
                                                    amount: item.amount,
                                                    remark: item.remark || '',
                                                    id: String(item?.id).indexOf('uuid') > -1 ? null : item.id,
                                                };
                                            }),
                                    };
                                    //校验金额
                                    const isQty = newData.items.every((item: any) => item.amount);
                                    if (!isQty) {
                                        message.error(intl.formatMessage({ id: '请完善金额' }));
                                        return;
                                    }
                                    if (
                                        !value?.footerData?.payments?.[0]?.id ||
                                        !value?.footerData?.payments?.[0]?.value
                                    ) {
                                        message.error(intl.formatMessage({ id: '完善付款信息' }));
                                        return;
                                    }
                                    if (billStatus.current === 100) {
                                        //提交单据
                                        newData.billStatus = 100;
                                    } else if (billStatus.current === 0) {
                                        //存为草稿
                                        newData.billStatus = 0;
                                    }

                                    if (mData?.id && !state?.isCopy) {
                                        //修改保存
                                        try {
                                            await runAsyncUpdate({ ...newData, id: mData?.id });
                                            message.success(intl.formatMessage({ id: '提交成功' }));
                                            navigate('/finance/revenue-expense/expense-bill');
                                        } catch (error) {}
                                    } else {
                                        //新增
                                        try {
                                            await runAsyncAdd({ data: newData });
                                            message.success(intl.formatMessage({ id: '提交成功' }));
                                            navigate('/finance/revenue-expense/expense-bill');
                                        } catch (error) {
                                            console.log(error);
                                        }
                                    }
                                }}
                            />
                        ),
                    },
                ]}
                onChange={onChange}
                tabBarExtraContent={
                    <>
                        <Space>
                            <div ref={portalTarget}></div>
                            <AuthComponent id="">
                                <Button
                                    type="secondary"
                                    onClick={() => {
                                        handlerAdd();
                                    }}
                                >
                                    <FormattedMessage id={'新增(N)'} />
                                </Button>
                            </AuthComponent>
                            <AuthComponent id="">
                                <Button onClick={() => {}}>
                                    <FormattedMessage id={'打印(P)'} />
                                </Button>
                            </AuthComponent>
                            {(mData?.status === 0 || !state?.id) && (
                                <AuthComponent id="">
                                    <Button
                                        onClick={() => {
                                            // handleClear();
                                        }}
                                    >
                                        <FormattedMessage id={'清空商品(Q)'} />
                                    </Button>
                                </AuthComponent>
                            )}

                            {(mData?.status === 0 || !state?.id) && (
                                <AuthComponent id="">
                                    <Button
                                        onClick={() => {
                                            billStatus.current = 0;
                                            ref.current.submit();
                                        }}
                                    >
                                        <FormattedMessage id={'存为草稿(C)'} />
                                    </Button>
                                </AuthComponent>
                            )}
                            {(mData?.status === 0 || !state?.id) && (
                                <AuthComponent id="">
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            billStatus.current = 100;
                                            ref.current.submit();
                                        }}
                                    >
                                        <FormattedMessage id={'提交单据(S)'} />
                                    </Button>
                                </AuthComponent>
                            )}
                        </Space>
                    </>
                }
            />
        </Spin>
    );
};

ExpenseBillOrder.displayName = 'ExpenseBillOrder';

export default ExpenseBillOrder;

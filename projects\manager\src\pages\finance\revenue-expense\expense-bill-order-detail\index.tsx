import React, { useEffect, useRef, useState } from 'react';
import { Button, Space } from '@common/components';
import { AuthComponent } from '@common/components/feature';
import { Spin, Tabs } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useKeyPress, useRequest } from 'ahooks';

import { useTags } from '@weway/ui';
import { useLocation } from 'react-router-dom';
import { postLatestcodeGet } from '@common/services/api/admin/mods/basic';
import DocumentDetail from './components/document-detail';
import { getFeeIncomeGet } from '@common/services/api/admin/mods/finance';
import OperationLog from './operation-log';

/**
 * 采购退货单详情页面
 * @returns
 */
const ExpenseBillOrderDetail = () => {
    const { navigate } = useTags();
    const { state } = useLocation(); //接收参数
    const portalTarget = useRef<HTMLDivElement>(null);
    const intl = useIntl();
    const [mData, setMData] = useState<any>({});
    const ref = useRef<any>();

    //生成单据编号
    const { runAsync: runAsyncGetCode } = useRequest(postLatestcodeGet, { manual: true });
    //获取详情
    const { loading: loadingGet, runAsync: runAsyncGet } = useRequest(getFeeIncomeGet, { manual: true });
    const onChange = (key: string) => {
        console.log(key);
    };
    const handleInit = async () => {
        try {
            const data: any = await runAsyncGet(state?.id, intl);
            data.id = state?.id;
            setMData(data);
        } catch (error) {}
    };
    const initCode = async () => {
        try {
            const { value } = await runAsyncGetCode({ type: 801 }, intl);
            setMData({
                ...mData,
                businessNumber: value,
            });
        } catch (error) {}
    };
    useEffect(() => {
        if (state?.id) {
            handleInit();
        } else {
            initCode();
        }
    }, [state?.id]);

    const handlerAdd = async () => {
        navigate('/finance/revenue-expense/expense-bill-order');
    };

    useKeyPress('alt.p', () => {});

    useKeyPress('alt.d', () => {});

    useKeyPress('alt.y', () => {});

    return (
        <Spin spinning={loadingGet}>
            <Tabs
                defaultActiveKey="1"
                items={[
                    {
                        key: '1',
                        label: <FormattedMessage id={'费用单详情'} />,
                        children: (
                            <DocumentDetail
                                getConfigSettingContainer={() => portalTarget}
                                ref={ref}
                                datas={mData}
                                isCopy={state?.isCopy}
                                onFinish={() => {}}
                            />
                        ),
                    },
                    {
                        key: '2',
                        label: <FormattedMessage id={'操作日志'} />,
                        children: <OperationLog id={state?.id} />,
                    },
                ]}
                onChange={onChange}
                tabBarExtraContent={
                    <>
                        <Space>
                            <div ref={portalTarget}></div>
                            <AuthComponent id="">
                                <Button
                                    type="secondary"
                                    onClick={() => {
                                        handlerAdd();
                                    }}
                                >
                                    <FormattedMessage id={'新增(N)'} />
                                </Button>
                            </AuthComponent>
                            <AuthComponent id="">
                                <Button onClick={() => {}}>
                                    <FormattedMessage id={'打印(P)'} />
                                </Button>
                            </AuthComponent>
                        </Space>
                    </>
                }
            />
        </Spin>
    );
};

ExpenseBillOrderDetail.displayName = 'ExpenseBillOrderDetail';

export default ExpenseBillOrderDetail;

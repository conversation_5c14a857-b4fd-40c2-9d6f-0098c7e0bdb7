import { GRID_FORM_MODAL_WIDTH } from '@/common';
import { Modal, GridInput, GridFormCollapse, GridSelect } from '@common/components';
import { Form, GridInputNumber, message } from '@weway/beacon';
import { FormattedMessage, useIntl } from '@weway/i18n';
import { useRequest } from 'ahooks';
import React, { useEffect } from 'react';
import { SelectUser } from '@/components/feature/basic-infomation/select-user';
import { useBankAccount } from '@/hooks/useBankAccount';
import { postTransferBillAdd } from '@common/services/api/admin/mods/finance';
type ModalPopProps = {
    open: boolean;
    formValue?: any;
    transferType: 'in' | 'out';
    handleModalResult?: (result: 'ok' | 'cancle') => void;
};
const ModalTransfer = (props: ModalPopProps) => {
    const [form] = Form.useForm();
    const { open = false, formValue, transferType, handleModalResult } = props;
    const intl = useIntl();
    const { loading, runAsync } = useRequest(postTransferBillAdd, {
        manual: true,
    });
    //保存
    const _onFinish = async (values: any) => {
        try {
            if (values.fromAccountId === values.toAccountId) {
                message.error(intl.formatMessage({ id: '转入转出账户不能相同!' }));
                return;
            }
            await runAsync(values, intl);
            handleModalResult('ok');
        } catch (error) {}
    };
    const handleCancel = () => {
        handleModalResult('cancle');
    };
    const formConfig = GridFormCollapse.getLayoutConfig({
        type: 'modal-small',
        columnCount: 5,
    });
    const { width } = Modal.getLayoutConfig({ columnCount: 5, maxColumns: formConfig.column });
    const { selectOptions: selectOptionsBankAccountData } = useBankAccount();

    useEffect(() => {
        if (open) {
            if (transferType === 'out' && formValue?.id) {
                form.setFieldValue('fromAccountId', formValue?.id);
            }
            if (transferType === 'in' && formValue?.id) {
                form.setFieldValue('toAccountId', formValue?.id);
            }
        }
    }, [formValue, open]);
    return (
        <Modal
            title={<FormattedMessage id="转账" />}
            open={open}
            onOk={form.submit}
            width={width}
            onCancel={handleCancel}
            okButtonProps={{ loading }}
            afterClose={form.resetFields}
        >
            <GridFormCollapse
                {...formConfig}
                form={form}
                defaultCollapsed={false}
                renderSubmitter={() => null}
                expandIcon={() => null}
                initialValues={{ mode: 0 }}
                onFinish={_onFinish}
            >
                <GridFormCollapse.Item
                    label={<FormattedMessage id={'转出账户'} />}
                    name="fromAccountId"
                    rules={[{ required: true, message: '请选择' }]}
                >
                    <GridSelect
                        options={selectOptionsBankAccountData}
                        placeholder={intl.formatMessage({ id: '请选择' })}
                    />
                </GridFormCollapse.Item>
                <GridFormCollapse.Item
                    label={<FormattedMessage id={'转入账户'} />}
                    name="toAccountId"
                    rules={[{ required: true, message: '请选择' }]}
                >
                    <GridSelect
                        options={selectOptionsBankAccountData}
                        placeholder={intl.formatMessage({ id: '请选择' })}
                    />
                </GridFormCollapse.Item>

                <GridFormCollapse.Item
                    label={<FormattedMessage id={'调整金额'} />}
                    name="amount"
                    rules={[{ required: true, message: '请输入调整金额' }]}
                >
                    <GridInputNumber style={{ width: GRID_FORM_MODAL_WIDTH }} />
                </GridFormCollapse.Item>

                <GridFormCollapse.Item
                    label={<FormattedMessage id={'经手人'} />}
                    name="handlerId"
                    rules={[{ required: true, message: '请选择经手人' }]}
                >
                    <SelectUser />
                </GridFormCollapse.Item>

                <GridFormCollapse.Item label={<FormattedMessage id={'备注'} />} name="remark">
                    <GridInput.TextArea />
                </GridFormCollapse.Item>
            </GridFormCollapse>
        </Modal>
    );
};
export default ModalTransfer;
ModalTransfer.displayName = 'ModalTransfer';
